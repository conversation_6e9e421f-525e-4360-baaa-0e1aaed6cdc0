/**
 * Z-Index Management Utilities
 * 
 * Comprehensive z-index management to prevent conflicts with host website elements.
 * Implements dynamic detection and adaptive z-index strategies.
 */

export interface ZIndexConfig {
  base: number;
  bubble: number;
  window: number;
  overlay: number;
  modal: number;
}

export interface ZIndexDetectionResult {
  maxDetected: number;
  conflictingElements: Element[];
  recommendedBase: number;
  strategy: 'static' | 'dynamic' | 'portal';
}

/**
 * Default z-index configuration with layered approach
 */
export const DEFAULT_Z_INDEX_CONFIG: ZIndexConfig = {
  base: 2147483640,      // Base layer for widget container
  bubble: 2147483641,    // Chat bubble (always visible)
  window: 2147483642,    // Chat window
  overlay: 2147483643,   // Overlays and dropdowns within widget
  modal: 2147483644,     // Modal dialogs within widget
};

/**
 * Common z-index ranges used by popular frameworks and libraries
 */
const COMMON_Z_INDEX_RANGES = {
  bootstrap: { modal: 1050, tooltip: 1070, popover: 1060 },
  materialUI: { modal: 1300, snackbar: 1400, tooltip: 1500 },
  antd: { modal: 1000, notification: 1010, message: 1020 },
  chakraUI: { modal: 1400, popover: 1500, tooltip: 1700 },
  semanticUI: { modal: 1001, popup: 1900, toast: 1901 },
  bulma: { modal: 40, navbar: 30 },
  foundation: { modal: 1000 },
  tailwind: { modal: 50 }, // z-50 class
};

/**
 * Detects existing z-index values on the page to avoid conflicts
 */
export function detectPageZIndexes(): ZIndexDetectionResult {
  const elements = document.querySelectorAll('*');
  const zIndexValues: number[] = [];
  const conflictingElements: Element[] = [];
  
  elements.forEach(element => {
    const computedStyle = window.getComputedStyle(element);
    const zIndex = computedStyle.zIndex;
    
    if (zIndex !== 'auto' && zIndex !== 'initial' && zIndex !== 'inherit') {
      const zIndexNum = parseInt(zIndex, 10);
      if (!isNaN(zIndexNum) && zIndexNum > 0) {
        zIndexValues.push(zIndexNum);
        
        // Consider elements with high z-index as potentially conflicting
        if (zIndexNum > 1000) {
          conflictingElements.push(element);
        }
      }
    }
  });
  
  const maxDetected = zIndexValues.length > 0 ? Math.max(...zIndexValues) : 0;
  
  // Determine strategy based on detected values
  let strategy: 'static' | 'dynamic' | 'portal' = 'static';
  let recommendedBase = DEFAULT_Z_INDEX_CONFIG.base;
  
  if (maxDetected > 2147483600) {
    // Very high z-indexes detected, use portal strategy
    strategy = 'portal';
    recommendedBase = 2147483647; // Maximum possible
  } else if (maxDetected > 10000) {
    // High z-indexes detected, use dynamic strategy
    strategy = 'dynamic';
    recommendedBase = maxDetected + 100;
  } else {
    // Normal range, use static strategy
    strategy = 'static';
    recommendedBase = Math.max(DEFAULT_Z_INDEX_CONFIG.base, maxDetected + 100);
  }
  
  return {
    maxDetected,
    conflictingElements,
    recommendedBase,
    strategy,
  };
}

/**
 * Creates a z-index configuration based on page analysis
 */
export function createAdaptiveZIndexConfig(): ZIndexConfig {
  const detection = detectPageZIndexes();
  const base = detection.recommendedBase;
  
  return {
    base: base,
    bubble: base + 1,
    window: base + 2,
    overlay: base + 3,
    modal: base + 4,
  };
}

/**
 * Applies z-index values to widget elements
 */
export function applyZIndexConfig(config: ZIndexConfig, containerElement: HTMLElement): void {
  // Apply base z-index to container
  containerElement.style.zIndex = config.base.toString();
  
  // Apply specific z-indexes to child elements
  const bubble = containerElement.querySelector('.webton-chat-bubble') as HTMLElement;
  if (bubble) {
    bubble.style.zIndex = config.bubble.toString();
  }
  
  const window = containerElement.querySelector('.webton-chat-window') as HTMLElement;
  if (window) {
    window.style.zIndex = config.window.toString();
  }
  
  // Apply to any overlays or modals within the widget
  const overlays = containerElement.querySelectorAll('.webton-overlay, .webton-dropdown');
  overlays.forEach(overlay => {
    (overlay as HTMLElement).style.zIndex = config.overlay.toString();
  });
  
  const modals = containerElement.querySelectorAll('.webton-modal');
  modals.forEach(modal => {
    (modal as HTMLElement).style.zIndex = config.modal.toString();
  });
}

/**
 * Creates a portal element for maximum z-index isolation
 */
export function createZIndexPortal(): HTMLElement {
  const portal = document.createElement('div');
  portal.id = 'webton-widget-portal';
  portal.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 2147483647 !important;
    overflow: visible !important;
  `;
  
  document.body.appendChild(portal);
  return portal;
}

/**
 * Monitors for z-index conflicts and adjusts if necessary
 */
export function startZIndexMonitoring(
  containerElement: HTMLElement,
  config: ZIndexConfig,
  onConflictDetected?: (newConfig: ZIndexConfig) => void
): () => void {
  let lastMaxZIndex = 0;
  
  const checkForConflicts = () => {
    const detection = detectPageZIndexes();
    
    if (detection.maxDetected > lastMaxZIndex && detection.maxDetected >= config.base) {
      console.warn('[WebtonWidget] Z-index conflict detected, adjusting...', {
        previousMax: lastMaxZIndex,
        newMax: detection.maxDetected,
        conflictingElements: detection.conflictingElements,
      });
      
      // Create new configuration
      const newConfig = createAdaptiveZIndexConfig();
      applyZIndexConfig(newConfig, containerElement);
      
      if (onConflictDetected) {
        onConflictDetected(newConfig);
      }
      
      lastMaxZIndex = detection.maxDetected;
    }
  };
  
  // Initial check
  checkForConflicts();
  
  // Set up periodic monitoring
  const intervalId = setInterval(checkForConflicts, 5000); // Check every 5 seconds
  
  // Set up mutation observer for immediate detection
  const observer = new MutationObserver(() => {
    // Debounce the check to avoid excessive calls
    setTimeout(checkForConflicts, 100);
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['style', 'class'],
  });
  
  // Return cleanup function
  return () => {
    clearInterval(intervalId);
    observer.disconnect();
  };
}

/**
 * Gets debug information about current z-index state
 */
export function getZIndexDebugInfo(): {
  detection: ZIndexDetectionResult;
  currentConfig: ZIndexConfig;
  recommendations: string[];
} {
  const detection = detectPageZIndexes();
  const currentConfig = createAdaptiveZIndexConfig();
  const recommendations: string[] = [];
  
  if (detection.conflictingElements.length > 0) {
    recommendations.push(`Found ${detection.conflictingElements.length} potentially conflicting elements`);
  }
  
  if (detection.maxDetected > 1000000) {
    recommendations.push('Consider using portal strategy for maximum isolation');
  }
  
  if (detection.strategy === 'dynamic') {
    recommendations.push('Using dynamic z-index adjustment');
  }
  
  return {
    detection,
    currentConfig,
    recommendations,
  };
}
