/**
 * Debug utilities for Supabase connection and database queries
 */

import { supabase } from '../lib/supabase';

/**
 * Test basic Supabase connection
 */
export const testSupabaseConnection = async () => {
  try {
    console.log('🔍 Testing Supabase connection...');
    
    // Test basic connection
    const { data, error } = await supabase.from('users').select('count', { count: 'exact' });
    
    if (error) {
      console.error('❌ Supabase connection test failed:', error);
      return { success: false, error };
    }
    
    console.log('✅ Supabase connection successful');
    console.log('📊 Users table count:', data);
    return { success: true, data };
  } catch (error) {
    console.error('💥 Exception testing Supabase connection:', error);
    return { success: false, error };
  }
};

/**
 * Test users table structure
 */
export const testUsersTableStructure = async () => {
  try {
    console.log('🔍 Testing users table structure...');
    
    // Try to select first row to see table structure
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Users table structure test failed:', error);
      return { success: false, error };
    }
    
    console.log('✅ Users table accessible');
    console.log('📋 Sample data structure:', data);
    return { success: true, data };
  } catch (error) {
    console.error('💥 Exception testing users table:', error);
    return { success: false, error };
  }
};

/**
 * Test specific user lookup
 */
export const testUserLookup = async (userId: string) => {
  try {
    console.log('🔍 Testing user lookup for ID:', userId);
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('❌ User lookup failed:', error);
      console.error('❌ Error code:', error.code);
      console.error('❌ Error message:', error.message);
      return { success: false, error };
    }
    
    console.log('✅ User found:', data);
    return { success: true, data };
  } catch (error) {
    console.error('💥 Exception in user lookup:', error);
    return { success: false, error };
  }
};

/**
 * Run all debug tests
 */
export const runAllDebugTests = async (userId?: string) => {
  console.log('🚀 Running Supabase debug tests...');
  
  await testSupabaseConnection();
  await testUsersTableStructure();
  
  if (userId) {
    await testUserLookup(userId);
  }
  
  console.log('🏁 Debug tests complete');
};
