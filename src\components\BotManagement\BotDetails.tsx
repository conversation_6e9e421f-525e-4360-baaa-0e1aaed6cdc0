/**
 * Bot Details Component
 * 
 * Detailed bot view with full configuration, analytics preview, and management options.
 */

import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  ArrowLeft,
  Bot as BotIcon,
  Edit,
  Settings,
  MessageSquare,
  Users,
  Calendar,
  Activity,
  FileText,
} from 'lucide-react';
import { getBot } from '../../services/botService';
import type { Database } from '../../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];

const BotDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bot, setBot] = useState<Bot | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadBot(id);
    }
  }, [id]);

  const loadBot = async (botId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await getBot(botId);
      setBot(data);
    } catch (err) {
      console.error('Error loading bot:', err);
      setError('Failed to load bot details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getBotTypeLabel = (type: string) => {
    switch (type) {
      case 'faq':
        return 'FAQ Bot';
      case 'lead_generation':
        return 'Lead Generation';
      default:
        return type;
    }
  };

  const getBotTypeColor = (type: string) => {
    switch (type) {
      case 'faq':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'lead_generation':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading bot details..." />
        </div>
      </div>
    );
  }

  if (error || !bot) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/bots')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bots
          </Button>
        </div>
        
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-center">
              <p>{error || 'Bot not found'}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => id && loadBot(id)}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const config = bot.config as any;

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/bots')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bots
          </Button>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <BotIcon className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{bot.name}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge 
                  variant="secondary" 
                  className={getBotTypeColor(bot.type)}
                >
                  {getBotTypeLabel(bot.type)}
                </Badge>
                <Badge 
                  variant={bot.status === 'active' ? 'default' : 'secondary'}
                  className={
                    bot.status === 'active' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                  }
                >
                  {bot.status}
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button asChild variant="outline">
              <Link to={`/bots/${bot.id}/edit`} className="flex items-center gap-2">
                <Edit className="h-4 w-4" />
                Edit Bot
              </Link>
            </Button>
            <Button asChild>
              <Link to={`/bots/${bot.id}/settings`} className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Bot Information */}
          <Card>
            <CardHeader>
              <CardTitle>Bot Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {config?.description && (
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-muted-foreground">{config.description}</p>
                </div>
              )}
              
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-1">Created</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(bot.created_at)}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Last Updated</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(bot.updated_at)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bot Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {config?.welcomeMessage && (
                <div>
                  <h4 className="font-medium mb-2">Welcome Message</h4>
                  <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                    "{config.welcomeMessage}"
                  </p>
                </div>
              )}
              
              {config?.fallbackMessage && (
                <div>
                  <h4 className="font-medium mb-2">Fallback Message</h4>
                  <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                    "{config.fallbackMessage}"
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Total Messages</span>
                </div>
                <span className="font-medium">0</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Conversations</span>
                </div>
                <span className="font-medium">0</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Knowledge Docs</span>
                </div>
                <span className="font-medium">0</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to={`/bots/${bot.id}/conversations`}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  View Conversations
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to={`/bots/${bot.id}/knowledge`}>
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Knowledge Base
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to={`/bots/${bot.id}/analytics`}>
                  <Activity className="h-4 w-4 mr-2" />
                  View Analytics
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BotDetails;
