/**
 * Bot Conversations Component
 * 
 * View and manage all conversations for a specific bot.
 */

import React from 'react';
import { MessageSquare } from 'lucide-react';
import ComingSoon from './ComingSoon';

const BotConversations: React.FC = () => {
  return (
    <ComingSoon
      featureName="Conversations"
      featureIcon={<MessageSquare className="h-8 w-8 text-primary" />}
      description="View, search, and analyze all conversations your bot has had with visitors. Monitor chat quality and identify common questions."
      expectedFeatures={[
        "Real-time conversation monitoring",
        "Search and filter conversations",
        "Export conversation transcripts",
        "Conversation analytics and insights",
        "User feedback and ratings",
        "Response time tracking",
        "Conversation tagging and categorization",
        "Automated conversation summaries"
      ]}
      estimatedCompletion="Coming Soon"
    />
  );
};

export default BotConversations;
