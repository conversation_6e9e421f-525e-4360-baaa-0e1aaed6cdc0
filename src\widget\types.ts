/**
 * Widget Types and Interfaces
 * 
 * TypeScript definitions for the standalone embeddable widget.
 */

/**
 * Widget configuration interface
 */
export interface WidgetConfig {
  // Required configuration
  botId: string;
  
  // Appearance configuration
  companyName?: string;
  botName?: string;
  primaryColor?: string;
  secondaryColor?: string;
  position?: 'bottom-right' | 'bottom-left';
  greetingMessage?: string;
  logo?: string;
  theme?: 'light' | 'dark' | 'auto';
  
  // API configuration
  apiEndpoint?: string;
  websocketEndpoint?: string;
  
  // Behavior configuration
  autoOpen?: boolean;
  showBranding?: boolean;
  enableFileUpload?: boolean;
  enableFeedback?: boolean;
  maxMessageLength?: number;
  
  // Security configuration
  allowedOrigins?: string[];
  csrfToken?: string;
  
  // Debug configuration
  debug?: boolean;
}

/**
 * Widget API interface
 */
export interface WidgetAPI {
  // Widget control methods
  open(): void;
  close(): void;
  toggle(): void;
  
  // Message methods
  sendMessage(message: string): void;
  
  // Event handling
  on(eventType: string, callback: Function): void;
  off(eventType: string, callback: Function): void;
  
  // Configuration methods
  updateConfig(updates: Partial<WidgetConfig>): void;
  getConfig(): WidgetConfig | null;
  
  // State methods
  isOpen(): boolean;
  isInitialized(): boolean;
  getSessionId(): string;

  // Debug methods
  getZIndexInfo(): any;

  // Utility methods
  destroy(): void;
}

/**
 * Message interface for widget communication
 */
export interface WidgetMessage {
  id: string;
  content: string;
  sender: 'user' | 'bot' | 'system';
  timestamp: Date;
  type?: 'text' | 'image' | 'file' | 'system';
  metadata?: Record<string, any>;
}

/**
 * Widget event types
 */
export type WidgetEventType = 
  | 'initialized'
  | 'opened'
  | 'closed'
  | 'message'
  | 'sendMessage'
  | 'configUpdated'
  | 'error'
  | 'destroyed'
  | 'conversationStarted'
  | 'conversationEnded'
  | 'typing'
  | 'connected'
  | 'disconnected';

/**
 * Widget event data interfaces
 */
export interface WidgetEventData {
  initialized: { config: WidgetConfig };
  opened: { sessionId: string };
  closed: { sessionId: string };
  message: { message: string; type: 'user' | 'bot'; sessionId: string; timestamp: string };
  sendMessage: { message: string };
  configUpdated: { config: WidgetConfig };
  error: { error: string; sessionId?: string };
  destroyed: {};
  conversationStarted: { sessionId: string };
  conversationEnded: { sessionId: string; reason?: string };
  typing: { isTyping: boolean; sessionId: string };
  connected: { sessionId: string };
  disconnected: { sessionId: string; reason?: string };
}

/**
 * Widget props for React component
 */
export interface StandaloneWidgetProps {
  config: WidgetConfig;
  isOpen: boolean;
  sessionId: string;
  onToggle: () => void;
  onMessage: (message: string, type: 'user' | 'bot') => void;
  onError: (error: string) => void;
}

/**
 * Widget container styles
 */
export interface WidgetContainerStyles {
  position: 'fixed';
  zIndex: number;
  bottom: string;
  right?: string;
  left?: string;
  fontFamily: string;
  fontSize: string;
  lineHeight: string;
  color: string;
  backgroundColor: string;
  border: string;
  borderRadius: string;
  boxShadow: string;
  maxWidth: string;
  maxHeight: string;
}

/**
 * Security configuration
 */
export interface SecurityConfig {
  allowedOrigins: string[];
  enableCSP: boolean;
  sanitizeInput: boolean;
  validateMessages: boolean;
  maxMessageLength: number;
  rateLimitRequests: boolean;
  rateLimitWindow: number;
  rateLimitMax: number;
}

/**
 * Widget analytics data
 */
export interface WidgetAnalytics {
  sessionId: string;
  botId: string;
  startTime: Date;
  endTime?: Date;
  messageCount: number;
  userMessages: number;
  botMessages: number;
  averageResponseTime: number;
  userSatisfaction?: number;
  conversationResolved?: boolean;
  userAgent: string;
  referrer: string;
  pageUrl: string;
}

/**
 * Widget error types
 */
export type WidgetErrorType = 
  | 'INITIALIZATION_ERROR'
  | 'CONFIGURATION_ERROR'
  | 'API_ERROR'
  | 'NETWORK_ERROR'
  | 'SECURITY_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Widget error interface
 */
export interface WidgetError {
  type: WidgetErrorType;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
  sessionId?: string;
}

/**
 * Widget state interface
 */
export interface WidgetState {
  isInitialized: boolean;
  isOpen: boolean;
  isConnected: boolean;
  isTyping: boolean;
  messageCount: number;
  lastActivity: Date;
  config: WidgetConfig | null;
  sessionId: string;
  errors: WidgetError[];
}

/**
 * Widget theme configuration
 */
export interface WidgetTheme {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  shadow: string;
  borderRadius: string;
  fontFamily: string;
  fontSize: string;
  fontWeight: string;
}

/**
 * Widget animation configuration
 */
export interface WidgetAnimation {
  duration: number;
  easing: string;
  scale: {
    initial: number;
    animate: number;
    exit: number;
  };
  opacity: {
    initial: number;
    animate: number;
    exit: number;
  };
  y: {
    initial: number;
    animate: number;
    exit: number;
  };
}

/**
 * Widget performance metrics
 */
export interface WidgetPerformance {
  initializationTime: number;
  firstRenderTime: number;
  averageResponseTime: number;
  memoryUsage: number;
  bundleSize: number;
  loadTime: number;
}

/**
 * Widget accessibility configuration
 */
export interface WidgetAccessibility {
  enableKeyboardNavigation: boolean;
  enableScreenReader: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  ariaLabels: Record<string, string>;
  focusManagement: boolean;
}

/**
 * Widget localization configuration
 */
export interface WidgetLocalization {
  language: string;
  direction: 'ltr' | 'rtl';
  translations: Record<string, string>;
  dateFormat: string;
  timeFormat: string;
  numberFormat: string;
}
