import React, {
  createContext,
  useEffect,
  useState,
  ReactNode,
  useCallback,
  useMemo,
} from "react";
import { User, Session, AuthError } from "@supabase/supabase-js";
import {
  supabase,
  ROLES,
  permissions,
  type UserRole,
} from "../lib/supabase";
import type { Database } from "../types/supabase";

/**
 * AuthContext – robuste Version mit getrennten Lade-Flags und sauberem Error‑Handling.
 *
 * - **authLoading**: lädt die Session (Supabase SDK)
 * - **profileLoading**: lädt das User‑Profil (DB‑Query)
 * - **loading**: komb<PERSON>ert beide Flags – einfach im UI zu verwenden
 *
 * Note: The useAuth hook is now in src/hooks/useAuth.ts to comply with Vite Fast Refresh requirements.
 */

/* ──────────────────────────────────────────
 * Typdefinitionen
 * ──────────────────────────────────────────*/
interface AuthContextType {
  // State
  user: User | null;
  session: Session | null;
  profile: Database["public"]["Tables"]["users"]["Row"] | null;
  loading: boolean;

  // Actions
  signIn: (
    email: string,
    password: string
  ) => Promise<{ error: AuthError | null }>;
  signUp: (
    email: string,
    password: string,
    fullName: string,
    companyName?: string
  ) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updateProfile: (
    updates: Partial<Database["public"]["Tables"]["users"]["Update"]>
  ) => Promise<{ error: Error | null }>;

  // Permissions
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  canManageUsers: boolean;
  canManageBots: boolean;
  canViewAnalytics: boolean;
  canEditSettings: boolean;
  canExportData: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

/* ──────────────────────────────────────────
 * Provider
 * ──────────────────────────────────────────*/
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // ── Raw State ───────────────────────────
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] =
    useState<Database["public"]["Tables"]["users"]["Row"] | null>(null);

  /**
   * Zwei getrennte Lade‑Flags, damit Profil‑Fetch die Session nicht blockiert
   * und vice versa.
   */
  const [authLoading, setAuthLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const loading = authLoading || profileLoading;

  // Debugging
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log("Auth state:", { user, profile, loading });
    }
  }, [user, profile, loading]);

  /* ────────────────────────────────────────
   * Helper: Profil laden
   * ────────────────────────────────────────*/
  const fetchUserProfile = useCallback(async (userId: string) => {
    try {
      console.log(supabase);
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("id", userId)
        .single();

        console.log("Data:", data);

      if (error) {
        console.error("Error fetching user profile:", error);
        setProfile(null);
        return { error };
      }

      setProfile(data);
    } catch (error) {
      console.error("Error fetching user profile:", error);
      setProfile(null);
      setProfileLoading(false);
      return { error: error as Error };
    }
  }, []);

  /* ────────────────────────────────────────
   * Initialisierung & Auth‑Listener
   * ────────────────────────────────────────*/
  useEffect(() => {
    const init = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user ?? null);
        console.log("Test (Init)");

        if (session?.user) {
          await fetchUserProfile(session.user.id);
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setAuthLoading(false);
      }
    };

    init();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        setAuthLoading(true);
        console.log("Test (OnAuthStateChange):", session.user.id);

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log("Fetching user profile...");
          await fetchUserProfile(session.user.id);
        } else {
          setProfile(null);
        }

        setAuthLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, [fetchUserProfile]);

  /* ────────────────────────────────────────
   * Actions
   * ────────────────────────────────────────*/
  /** Sign in */
  const signIn = useCallback(async (email: string, password: string) => {
    setAuthLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    } catch (error) {
      console.error("Sign in error:", error);
      return { error: error as AuthError };
    } finally {
      setAuthLoading(false);
    }
  }, []);

  /** Sign up */
  const signUp = useCallback(
    async (
      email: string,
      password: string,
      fullName: string,
      companyName?: string
    ) => {
      setAuthLoading(true);
      try {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName,
              company_name: companyName,
              role: ROLES.CLIENT,
            },
          },
        });

        if (error) return { error };

        // Profil anlegen
        if (data.user) {
          const { error: profileError } = await supabase.from("users").insert({
            id: data.user.id,
            email: data.user.email!,
            full_name: fullName,
            company_name: companyName,
            role: ROLES.CLIENT,
          });
          if (profileError) console.error("Error creating profile:", profileError);
        }
        return { error: null };
      } catch (error) {
        console.error("Sign up error:", error);
        return { error: error as AuthError };
      } finally {
        setAuthLoading(false);
      }
    },
    []
  );

  /** Sign out */
  const signOut = useCallback(async () => {
    setAuthLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      // Lokalen State zurücksetzen
      setSession(null);
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    } finally {
      setAuthLoading(false);
    }
  }, []);

  /** Reset password */
  const resetPassword = useCallback(async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      return { error };
    } catch (error) {
      console.error("Reset password error:", error);
      return { error: error as AuthError };
    }
  }, []);

  /** Update profile */
  const updateProfile = useCallback(
    async (
      updates: Partial<Database["public"]["Tables"]["users"]["Update"]>
    ) => {
      if (!user) return { error: new Error("No user logged in") };

      try {
        const { error } = await supabase
          .from("users")
          .update(updates)
          .eq("id", user.id);

        if (error) return { error };
        await fetchUserProfile(user.id);
        return { error: null };
      } catch (error) {
        console.error("Update profile error:", error);
        return { error: error as Error };
      }
    },
    [user, fetchUserProfile]
  );

  /* ────────────────────────────────────────
   * Permissions
   * ────────────────────────────────────────*/
  const hasRole = useCallback((role: UserRole) => profile?.role === role, [
    profile,
  ]);

  const hasAnyRole = useCallback(
    (roles: UserRole[]) => (profile?.role ? roles.includes(profile.role as UserRole) : false),
    [profile]
  );

  // Berechnete Rechte
  const canManageUsers = useMemo(
    () => permissions.canManageUsers(profile),
    [profile]
  );
  const canManageBots = useMemo(
    () => permissions.canManageBots(profile),
    [profile]
  );
  const canViewAnalytics = useMemo(
    () => permissions.canViewAnalytics(profile),
    [profile]
  );
  const canEditSettings = useMemo(
    () => permissions.canEditSettings(profile),
    [profile]
  );
  const canExportData = useMemo(
    () => permissions.canExportData(profile),
    [profile]
  );

  /* ────────────────────────────────────────
   * Context‑Value (memoisiert)
   * ────────────────────────────────────────*/
  const value = useMemo<AuthContextType>(
    () => ({
      // State
      user,
      session,
      profile,
      loading,

      // Actions
      signIn,
      signUp,
      signOut,
      resetPassword,
      updateProfile,

      // Permissions
      hasRole,
      hasAnyRole,
      canManageUsers,
      canManageBots,
      canViewAnalytics,
      canEditSettings,
      canExportData,
    }),
    [
      user,
      session,
      profile,
      loading,
      signIn,
      signUp,
      signOut,
      resetPassword,
      updateProfile,
      hasRole,
      hasAnyRole,
      canManageUsers,
      canManageBots,
      canViewAnalytics,
      canEditSettings,
      canExportData,
    ]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
