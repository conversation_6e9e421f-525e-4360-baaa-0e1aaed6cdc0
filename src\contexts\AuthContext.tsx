import React, {
  useEffect,
  useState,
  ReactNode,
  useC<PERSON>back,
  useMemo,
} from "react";
import { User, Session, AuthError } from "@supabase/supabase-js";
import {
  supabase,
  ROLES,
  permissions,
  type UserRole,
} from "../lib/supabase";

import { AuthContext, AuthContextType, UserProfile } from "./AuthContextDefinition";

/**
 * AuthContext – robuste Version mit getrennten Lade-Flags und sauberem Error‑Handling.
 *
 * - **authLoading**: lädt die Session (Supabase SDK)
 * - **profileLoading**: lädt das User‑Profil (DB‑Query)
 * - **loading**: komb<PERSON>ert beide Flags – einfach im UI zu verwenden
 *
 * Note: The useAuth hook is now in src/hooks/useAuth.ts to comply with Vite Fast Refresh requirements.
 */

/* ──────────────────────────────────────────
 * Types imported from AuthContextDefinition.ts
 * ──────────────────────────────────────────*/

/* ──────────────────────────────────────────
 * Provider
 * ──────────────────────────────────────────*/
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // ── Raw State ───────────────────────────
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);

  /**
   * Zwei getrennte Lade‑Flags, damit Profil‑Fetch die Session nicht blockiert
   * und vice versa.
   */
  const [authLoading, setAuthLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const loading = authLoading || profileLoading;

  // Debugging
  useEffect(() => {
    if (import.meta.env.DEV) {
      console.log("Auth state:", { user, profile, loading });
    }
  }, [user, profile, loading]);

  /* ────────────────────────────────────────
   * Helper: Profil laden
   * ────────────────────────────────────────*/
  const fetchUserProfile = useCallback(async (userId: string, userSession?: Session | null) => {
    console.log("🔍 Starting profile fetch for user:", userId);
    console.log("🔍 Current state - profileLoading:", profileLoading, "profile:", profile?.id);

    // Prevent duplicate calls if already loading or profile exists
    if (profileLoading || (profile && profile.id === userId)) {
      console.log("🚫 Skipping profile fetch - already loading or profile exists");
      return { error: null };
    }

    console.log("✅ Proceeding with profile fetch");
    console.log("🔄 Setting profileLoading to true...");
    setProfileLoading(true);
    console.log("🔄 ProfileLoading set, entering try block...");

    try {
      console.log("🔄 Inside try block, using existing session...");
      // Use the session we already have instead of fetching it again
      const currentSession = userSession || session;
      console.log("🔐 Current session before query:", currentSession?.access_token ? "✅ Has token" : "❌ No token");
      console.log("🔐 Session user ID:", currentSession?.user?.id);
      console.log("📡 Making Supabase query...");

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout after 10 seconds')), 10000)
      );

      const queryPromise = supabase
        .from("users")
        .select("*")
        .eq("id", userId)
        .single();

      console.log("⏱️ Starting query with 10s timeout...");
      const { data, error } = await Promise.race([queryPromise, timeoutPromise]) as any;

      console.log("📋 Query completed - Data:", data, "Error:", error);

      if (error) {
        console.error("❌ Supabase error:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        setProfile(null);
        setProfileLoading(false);
        return { error };
      }

      if (!data) {
        console.warn("⚠️ No profile data returned for user:", userId);
        setProfile(null);
        setProfileLoading(false);
        return { error: new Error("No profile data found") };
      }

      console.log("✅ Profile loaded successfully:", data);
      setProfile(data);
      setProfileLoading(false);
      return { error: null };

    } catch (error) {
      console.error("💥 Profile fetch failed:", error);

      // Check if it's a timeout error
      if (error instanceof Error && error.message.includes('timeout')) {
        console.error("⏰ Query timed out - possible network or connectivity issue");
      }

      setProfile(null);
      setProfileLoading(false);
      return { error: error as Error };
    }
  }, [profileLoading, profile]);

  /* ────────────────────────────────────────
   * Initialisierung & Auth‑Listener
   * ────────────────────────────────────────*/
  useEffect(() => {
    const init = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user ?? null);
        console.log("Test (Init)");

        if (session?.user) {
          await fetchUserProfile(session.user.id);
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setAuthLoading(false);
      }
    };

    init();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log("🔄 Auth state change event:", event);
        console.log("🔄 Session:", session);
        console.log("🔄 User ID:", session?.user?.id);

        setSession(session);
        setUser(session?.user ?? null);

        // Fetch profile for both SIGNED_IN and INITIAL_SESSION events
        // The duplicate prevention logic in fetchUserProfile will handle redundant calls
        if (session?.user) {
          console.log("👤 User authenticated, fetching profile...");
          await fetchUserProfile(session.user.id, session);
        } else {
          console.log("❌ No user session, clearing profile");
          setProfile(null);
          setProfileLoading(false);
        }

        console.log("✅ Auth state change complete");
      }
    );

    return () => subscription.unsubscribe();
  }, [fetchUserProfile]);

  /* ────────────────────────────────────────
   * Actions
   * ────────────────────────────────────────*/
  /** Sign in */
  const signIn = useCallback(async (email: string, password: string) => {
    setAuthLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { error };
    } catch (error) {
      console.error("Sign in error:", error);
      return { error: error as AuthError };
    } finally {
      setAuthLoading(false);
    }
  }, []);

  /** Sign up */
  const signUp = useCallback(
    async (
      email: string,
      password: string,
      fullName: string,
      companyName?: string
    ) => {
      setAuthLoading(true);
      try {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              full_name: fullName,
              company_name: companyName,
              role: ROLES.CLIENT,
            },
          },
        });

        if (error) return { error };

        // Profil anlegen
        if (data.user) {
          const { error: profileError } = await supabase.from("users").insert({
            id: data.user.id,
            email: data.user.email!,
            full_name: fullName,
            company_name: companyName,
            role: ROLES.CLIENT,
          });
          if (profileError) console.error("Error creating profile:", profileError);
        }
        return { error: null };
      } catch (error) {
        console.error("Sign up error:", error);
        return { error: error as AuthError };
      } finally {
        setAuthLoading(false);
      }
    },
    []
  );

  /** Sign out */
  const signOut = useCallback(async () => {
    setAuthLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      // Lokalen State zurücksetzen
      setSession(null);
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    } finally {
      setAuthLoading(false);
    }
  }, []);

  /** Reset password */
  const resetPassword = useCallback(async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      return { error };
    } catch (error) {
      console.error("Reset password error:", error);
      return { error: error as AuthError };
    }
  }, []);

  /** Update profile */
  const updateProfile = useCallback(
    async (updates: Partial<UserProfile>) => {
      if (!user) return { error: new Error("No user logged in") };

      try {
        const { error } = await supabase
          .from("users")
          .update(updates)
          .eq("id", user.id);

        if (error) return { error };
        await fetchUserProfile(user.id);
        return { error: null };
      } catch (error) {
        console.error("Update profile error:", error);
        return { error: error as Error };
      }
    },
    [user, fetchUserProfile]
  );

  /* ────────────────────────────────────────
   * Permissions
   * ────────────────────────────────────────*/
  const hasRole = useCallback((role: UserRole) => profile?.role === role, [
    profile,
  ]);

  const hasAnyRole = useCallback(
    (roles: UserRole[]) => (profile?.role ? roles.includes(profile.role as UserRole) : false),
    [profile]
  );

  // Berechnete Rechte
  const canManageUsers = useMemo(
    () => permissions.canManageUsers(profile),
    [profile]
  );
  const canManageBots = useMemo(
    () => permissions.canManageBots(profile),
    [profile]
  );
  const canViewAnalytics = useMemo(
    () => permissions.canViewAnalytics(profile),
    [profile]
  );
  const canEditSettings = useMemo(
    () => permissions.canEditSettings(profile),
    [profile]
  );
  const canExportData = useMemo(
    () => permissions.canExportData(profile),
    [profile]
  );

  /* ────────────────────────────────────────
   * Context‑Value (memoisiert)
   * ────────────────────────────────────────*/
  const value = useMemo<AuthContextType>(
    () => ({
      // State
      user,
      session,
      profile,
      loading,

      // Actions
      signIn,
      signUp,
      signOut,
      resetPassword,
      updateProfile,

      // Permissions
      hasRole,
      hasAnyRole,
      canManageUsers,
      canManageBots,
      canViewAnalytics,
      canEditSettings,
      canExportData,
    }),
    [
      user,
      session,
      profile,
      loading,
      signIn,
      signUp,
      signOut,
      resetPassword,
      updateProfile,
      hasRole,
      hasAnyRole,
      canManageUsers,
      canManageBots,
      canViewAnalytics,
      canEditSettings,
      canExportData,
    ]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
