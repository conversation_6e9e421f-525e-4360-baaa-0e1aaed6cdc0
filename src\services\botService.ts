/**
 * Bot Service
 * 
 * Service layer for bot management operations using Supabase.
 * Handles CRUD operations for bots with proper error handling and TypeScript types.
 */

import { supabase } from '../lib/supabase';
import type { Database } from '../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];
type BotInsert = Database['public']['Tables']['bots']['Insert'];
type BotUpdate = Database['public']['Tables']['bots']['Update'];

/**
 * Get all bots for the authenticated user.
 */
export async function getBots(): Promise<Bot[]> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('bots')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching bots:', error);
    throw new Error(`Failed to fetch bots: ${error.message}`);
  }

  return data || [];
}

/**
 * Get a specific bot by ID.
 */
export async function getBot(botId: string): Promise<Bot> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('bots')
    .select('*')
    .eq('id', botId)
    .eq('user_id', user.id)
    .single();

  if (error) {
    console.error('Error fetching bot:', error);
    if (error.code === 'PGRST116') {
      throw new Error('Bot not found');
    }
    throw new Error(`Failed to fetch bot: ${error.message}`);
  }

  return data;
}

/**
 * Create a new bot.
 */
export async function createBot(botData: Omit<BotInsert, 'user_id'>): Promise<Bot> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('bots')
    .insert({
      ...botData,
      user_id: user.id,
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating bot:', error);
    throw new Error(`Failed to create bot: ${error.message}`);
  }

  return data;
}

/**
 * Update an existing bot.
 */
export async function updateBot(botId: string, updates: BotUpdate): Promise<Bot> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Ensure user owns the bot
  const existingBot = await getBot(botId);
  if (existingBot.user_id !== user.id) {
    throw new Error('Unauthorized: You can only update your own bots');
  }

  const { data, error } = await supabase
    .from('bots')
    .update({
      ...updates,
      updated_at: new Date().toISOString(),
    })
    .eq('id', botId)
    .eq('user_id', user.id)
    .select()
    .single();

  if (error) {
    console.error('Error updating bot:', error);
    throw new Error(`Failed to update bot: ${error.message}`);
  }

  return data;
}

/**
 * Delete a bot.
 */
export async function deleteBot(botId: string): Promise<void> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Ensure user owns the bot
  const existingBot = await getBot(botId);
  if (existingBot.user_id !== user.id) {
    throw new Error('Unauthorized: You can only delete your own bots');
  }

  const { error } = await supabase
    .from('bots')
    .delete()
    .eq('id', botId)
    .eq('user_id', user.id);

  if (error) {
    console.error('Error deleting bot:', error);
    throw new Error(`Failed to delete bot: ${error.message}`);
  }
}

/**
 * Toggle bot status (active/inactive).
 */
export async function toggleBotStatus(botId: string): Promise<Bot> {
  const bot = await getBot(botId);
  const newStatus = bot.status === 'active' ? 'inactive' : 'active';
  
  return updateBot(botId, { status: newStatus });
}

/**
 * Get bot statistics.
 */
export async function getBotStats(botId: string): Promise<{
  totalMessages: number;
  totalConversations: number;
  totalDocuments: number;
}> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  // Ensure user owns the bot
  await getBot(botId);

  // Get message count
  const { count: messageCount } = await supabase
    .from('messages')
    .select('*', { count: 'exact', head: true })
    .eq('bot_id', botId);

  // Get conversation count
  const { count: conversationCount } = await supabase
    .from('conversations')
    .select('*', { count: 'exact', head: true })
    .eq('bot_id', botId);

  // Get document count
  const { count: documentCount } = await supabase
    .from('knowledge_documents')
    .select('*', { count: 'exact', head: true })
    .eq('bot_id', botId);

  return {
    totalMessages: messageCount || 0,
    totalConversations: conversationCount || 0,
    totalDocuments: documentCount || 0,
  };
}

/**
 * Search bots by name.
 */
export async function searchBots(query: string): Promise<Bot[]> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('bots')
    .select('*')
    .eq('user_id', user.id)
    .ilike('name', `%${query}%`)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error searching bots:', error);
    throw new Error(`Failed to search bots: ${error.message}`);
  }

  return data || [];
}

/**
 * Get bots by status.
 */
export async function getBotsByStatus(status: 'active' | 'inactive'): Promise<Bot[]> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }

  const { data, error } = await supabase
    .from('bots')
    .select('*')
    .eq('user_id', user.id)
    .eq('status', status)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching bots by status:', error);
    throw new Error(`Failed to fetch bots by status: ${error.message}`);
  }

  return data || [];
}
