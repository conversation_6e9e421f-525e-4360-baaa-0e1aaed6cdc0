/**
 * Analytics Dashboard Component
 * 
 * Main analytics dashboard with comprehensive metrics, charts, and export functionality.
 * Displays key performance indicators, time series data, and detailed analytics.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  MessageSquare, 
  Clock, 
  Star,
  Download,
  Calendar as CalendarIcon,
  RefreshCw,
  Filter,
  ArrowUpRight,
  ArrowDownRight,
} from 'lucide-react';
import { format, subDays, startOfDay, endOfDay } from 'date-fns';
import { cn } from '@/lib/utils';

import { AnalyticsService } from '@/services/analyticsService';
import { AnalyticsMetrics, TimeSeriesData } from '@/types/api';
import { MetricsOverview } from './MetricsOverview';
import { ConversationChart } from './ConversationChart';
import { SatisfactionChart } from './SatisfactionChart';
import { TopicsChart } from './TopicsChart';
import { ConversationFunnel } from './ConversationFunnel';
import { ExportDialog } from './ExportDialog';

interface AnalyticsDashboardProps {
  botId?: string;
  className?: string;
}

export function AnalyticsDashboard({ 
  botId = 'demo-bot-123', 
  className 
}: AnalyticsDashboardProps) {
  // State management
  const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Date range state
  const [dateRange, setDateRange] = useState({
    from: startOfDay(subDays(new Date(), 30)),
    to: endOfDay(new Date()),
  });
  const [granularity, setGranularity] = useState<'hour' | 'day' | 'week' | 'month'>('day');
  
  // UI state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  /**
   * Load analytics data
   */
  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await AnalyticsService.getAnalytics(
        botId,
        dateRange.from.toISOString(),
        dateRange.to.toISOString(),
        granularity
      );
      
      setMetrics(response.metrics);
      setTimeSeriesData(response.time_series || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics data');
      console.error('Analytics loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle date range change
   */
  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range);
  };

  /**
   * Handle quick date range selection
   */
  const handleQuickDateRange = (days: number) => {
    setDateRange({
      from: startOfDay(subDays(new Date(), days)),
      to: endOfDay(new Date()),
    });
  };

  /**
   * Handle granularity change
   */
  const handleGranularityChange = (newGranularity: 'hour' | 'day' | 'week' | 'month') => {
    setGranularity(newGranularity);
  };

  /**
   * Calculate percentage change from previous period
   */
  const calculateChange = (current: number, previous: number): { value: number; isPositive: boolean } => {
    if (previous === 0) return { value: 0, isPositive: true };
    const change = ((current - previous) / previous) * 100;
    return { value: Math.abs(change), isPositive: change >= 0 };
  };

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadAnalyticsData();
  }, [botId, dateRange, granularity]);

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h2>
            <p className="text-muted-foreground">Loading analytics data...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Error loading analytics: {error}</p>
              <Button onClick={loadAnalyticsData} variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header with controls */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Track performance and insights for your chatbot
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Quick date range buttons */}
          <div className="flex items-center gap-1">
            {[7, 30, 90].map((days) => (
              <Button
                key={days}
                variant="outline"
                size="sm"
                onClick={() => handleQuickDateRange(days)}
                className={cn(
                  "text-xs",
                  Math.abs(dateRange.to.getTime() - dateRange.from.getTime()) === days * 24 * 60 * 60 * 1000 &&
                  "bg-primary text-primary-foreground"
                )}
              >
                {days}d
              </Button>
            ))}
          </div>
          
          {/* Granularity selector */}
          <Select value={granularity} onValueChange={handleGranularityChange}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hour">Hour</SelectItem>
              <SelectItem value="day">Day</SelectItem>
              <SelectItem value="week">Week</SelectItem>
              <SelectItem value="month">Month</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Export button */}
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setShowExportDialog(true)}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          
          {/* Refresh button */}
          <Button 
            variant="outline" 
            size="sm"
            onClick={loadAnalyticsData}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Date range display */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <CalendarIcon className="h-4 w-4" />
        <span>
          {format(dateRange.from, 'MMM dd, yyyy')} - {format(dateRange.to, 'MMM dd, yyyy')}
        </span>
        <Badge variant="secondary" className="text-xs">
          {Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days
        </Badge>
      </div>

      {/* Metrics Overview */}
      {metrics && (
        <MetricsOverview 
          metrics={metrics} 
          timeSeriesData={timeSeriesData}
          dateRange={dateRange}
        />
      )}

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="conversations">Conversations</TabsTrigger>
          <TabsTrigger value="satisfaction">Satisfaction</TabsTrigger>
          <TabsTrigger value="topics">Topics</TabsTrigger>
          <TabsTrigger value="funnel">Funnel</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ConversationChart 
              data={timeSeriesData} 
              granularity={granularity}
            />
            <SatisfactionChart 
              botId={botId}
              dateRange={dateRange}
            />
          </div>
        </TabsContent>

        <TabsContent value="conversations" className="space-y-4">
          <ConversationChart 
            data={timeSeriesData} 
            granularity={granularity}
            detailed={true}
          />
        </TabsContent>

        <TabsContent value="satisfaction" className="space-y-4">
          <SatisfactionChart 
            botId={botId}
            dateRange={dateRange}
            detailed={true}
          />
        </TabsContent>

        <TabsContent value="topics" className="space-y-4">
          <TopicsChart 
            botId={botId}
            dateRange={dateRange}
          />
        </TabsContent>

        <TabsContent value="funnel" className="space-y-4">
          <ConversationFunnel 
            botId={botId}
            dateRange={dateRange}
          />
        </TabsContent>
      </Tabs>

      {/* Export Dialog */}
      <ExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        botId={botId}
        dateRange={dateRange}
        metrics={metrics}
        timeSeriesData={timeSeriesData}
      />
    </div>
  );
}
