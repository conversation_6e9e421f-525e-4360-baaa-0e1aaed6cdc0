/**
 * useAuth Hook Unit Tests
 * 
 * Tests for the useAuth hook functionality and error handling.
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { useAuth } from '../../../src/hooks/useAuth';
import { AuthProvider } from '../../../src/contexts/AuthContext';

// Test component that uses the hook
const TestComponent = () => {
  const { user, loading } = useAuth();
  return (
    <div>
      <div data-testid="user">{user ? 'authenticated' : 'not authenticated'}</div>
      <div data-testid="loading">{loading ? 'loading' : 'not loading'}</div>
    </div>
  );
};

// Test component without provider
const TestComponentWithoutProvider = () => {
  const { user } = useAuth();
  return <div>{user ? 'authenticated' : 'not authenticated'}</div>;
};

describe('useAuth Hook', () => {
  it('should work when used within AuthProvider', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Should render without throwing
    expect(screen.getByTestId('user')).toBeInTheDocument();
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('should throw error when used outside AuthProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponentWithoutProvider />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });
});
