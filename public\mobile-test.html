<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Responsiveness Test - Webton Chatbot</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4f46e5;
        }
        
        .test-section h2 {
            color: #4f46e5;
            margin-top: 0;
        }
        
        .device-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .instructions {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 10px 20px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #4338ca;
        }
        
        .content-area {
            height: 200px;
            background: #f1f3f4;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .test-buttons {
                flex-direction: column;
            }
            
            .test-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Mobile Responsiveness Test</h1>
        
        <div class="device-info">
            <strong>Current Device Info:</strong><br>
            Screen Width: <span id="screenWidth"></span>px<br>
            Screen Height: <span id="screenHeight"></span>px<br>
            Viewport Width: <span id="viewportWidth"></span>px<br>
            Viewport Height: <span id="viewportHeight"></span>px<br>
            Device Pixel Ratio: <span id="devicePixelRatio"></span><br>
            User Agent: <span id="userAgent"></span>
        </div>
        
        <div class="test-section">
            <h2>🎯 Test Instructions</h2>
            <div class="instructions">
                <strong>How to test mobile responsiveness:</strong>
                <ol>
                    <li>Open browser developer tools (F12)</li>
                    <li>Toggle device toolbar (Ctrl+Shift+M)</li>
                    <li>Test different device sizes:
                        <ul>
                            <li>iPhone SE (375×667)</li>
                            <li>iPhone 12 Pro (390×844)</li>
                            <li>iPad (768×1024)</li>
                            <li>Galaxy S20 Ultra (412×915)</li>
                        </ul>
                    </li>
                    <li>Test both portrait and landscape orientations</li>
                    <li>Click the "Open Chat Widget" button below</li>
                    <li>Verify the widget displays properly on all screen sizes</li>
                </ol>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🚀 Widget Test Controls</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="openWidget()">Open Chat Widget</button>
                <button class="test-btn" onclick="closeWidget()">Close Widget</button>
                <button class="test-btn" onclick="toggleWidget()">Toggle Widget</button>
                <button class="test-btn" onclick="changePosition()">Change Position</button>
                <button class="test-btn" onclick="updateDeviceInfo()">Refresh Device Info</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Expected Behavior</h2>
            <ul>
                <li><strong>Mobile (≤480px):</strong> Widget should take full width with proper margins</li>
                <li><strong>Tablet (481-768px):</strong> Widget should be 380px wide with responsive height</li>
                <li><strong>Desktop (>768px):</strong> Widget should maintain standard 350px width</li>
                <li><strong>Touch Targets:</strong> All buttons should be at least 44px for easy tapping</li>
                <li><strong>Text Size:</strong> Input should use 16px font to prevent iOS zoom</li>
                <li><strong>Landscape Mode:</strong> Widget should adapt height appropriately</li>
            </ul>
        </div>
        
        <div class="content-area">
            <p>Sample content area to test widget positioning</p>
        </div>
    </div>

    <!-- Load the widget -->
    <script src="embed.js"></script>
    
    <script>
        let widgetPosition = 'bottom-right';
        
        // Initialize device info
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = screen.width;
            document.getElementById('screenHeight').textContent = screen.height;
            document.getElementById('viewportWidth').textContent = window.innerWidth;
            document.getElementById('viewportHeight').textContent = window.innerHeight;
            document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
            document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...';
        }
        
        // Widget control functions
        function openWidget() {
            if (window.WebtonChatbot) {
                window.WebtonChatbot.open();
            }
        }
        
        function closeWidget() {
            if (window.WebtonChatbot) {
                window.WebtonChatbot.close();
            }
        }
        
        function toggleWidget() {
            if (window.WebtonChatbot) {
                window.WebtonChatbot.toggle();
            }
        }
        
        function changePosition() {
            widgetPosition = widgetPosition === 'bottom-right' ? 'bottom-left' : 'bottom-right';
            
            // Reinitialize widget with new position
            if (window.WebtonChatbot) {
                window.WebtonChatbot.destroy();
            }
            
            setTimeout(() => {
                window.WebtonChatbot.init({
                    botId: 'test-bot',
                    apiEndpoint: 'https://api.webton-chatbots.com/v1',
                    position: widgetPosition,
                    theme: {
                        primaryColor: '#4f46e5',
                        secondaryColor: '#ffffff'
                    },
                    branding: {
                        companyName: 'Test Company',
                        logo: 'https://api.dicebear.com/7.x/avataaars/svg?seed=test'
                    }
                });
            }, 100);
        }
        
        // Update device info on load and resize
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        
        // Initialize the widget
        window.addEventListener('load', function() {
            if (window.WebtonChatbot) {
                window.WebtonChatbot.init({
                    botId: 'test-bot',
                    apiEndpoint: 'https://api.webton-chatbots.com/v1',
                    position: 'bottom-right',
                    theme: {
                        primaryColor: '#4f46e5',
                        secondaryColor: '#ffffff'
                    },
                    branding: {
                        companyName: 'Mobile Test',
                        logo: 'https://api.dicebear.com/7.x/avataaars/svg?seed=mobile'
                    }
                });
            }
        });
    </script>
</body>
</html>
