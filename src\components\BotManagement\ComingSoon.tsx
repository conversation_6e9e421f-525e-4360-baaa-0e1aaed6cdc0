/**
 * Coming Soon Component
 * 
 * Reusable placeholder component for features under development.
 * Provides consistent UI and navigation for incomplete features.
 */

import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  ArrowLeft,
  Bot as BotIcon,
  Construction,
  Calendar,
  Lightbulb,
} from 'lucide-react';
import { getBot } from '../../services/botService';
import type { Database } from '../../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];

interface ComingSoonProps {
  featureName: string;
  featureIcon: React.ReactNode;
  description: string;
  expectedFeatures?: string[];
  estimatedCompletion?: string;
}

const ComingSoon: React.FC<ComingSoonProps> = ({
  featureName,
  featureIcon,
  description,
  expectedFeatures = [],
  estimatedCompletion = "Coming Soon",
}) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bot, setBot] = useState<Bot | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadBot(id);
    }
  }, [id]);

  const loadBot = async (botId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await getBot(botId);
      setBot(data);
    } catch (err) {
      console.error('Error loading bot:', err);
      setError('Failed to load bot information.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading..." />
        </div>
      </div>
    );
  }

  if (error || !bot) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/bots')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bots
          </Button>
        </div>
        
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-center">
              <p>{error || 'Bot not found'}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => id && loadBot(id)}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/bots/${bot.id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bot Details
          </Button>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="p-3 bg-primary/10 rounded-lg">
            {featureIcon}
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {featureName} - {bot.name}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300">
                {estimatedCompletion}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Feature Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Construction className="h-5 w-5 text-orange-500" />
                Feature in Development
              </CardTitle>
              <CardDescription>
                We're working hard to bring you this feature
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                {description}
              </p>
              
              {expectedFeatures.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Lightbulb className="h-4 w-4 text-yellow-500" />
                    Planned Features
                  </h4>
                  <ul className="space-y-1">
                    {expectedFeatures.map((feature, index) => (
                      <li key={index} className="text-sm text-muted-foreground flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Bot Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BotIcon className="h-5 w-5" />
                Current Bot
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-1">Bot Name</h4>
                <p className="text-sm text-muted-foreground">{bot.name}</p>
              </div>
              
              <div>
                <h4 className="font-medium mb-1">Type</h4>
                <Badge variant="outline">
                  {bot.type === 'faq' ? 'FAQ Bot' : 'Lead Generation'}
                </Badge>
              </div>
              
              <div>
                <h4 className="font-medium mb-1">Status</h4>
                <Badge 
                  variant={bot.status === 'active' ? 'default' : 'secondary'}
                  className={
                    bot.status === 'active' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                  }
                >
                  {bot.status}
                </Badge>
              </div>

              <div>
                <h4 className="font-medium mb-1">Created</h4>
                <p className="text-sm text-muted-foreground">
                  {new Date(bot.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 mt-8">
          <Button asChild variant="outline">
            <Link to={`/bots/${bot.id}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Bot Details
            </Link>
          </Button>
          
          <Button asChild>
            <Link to={`/bots/${bot.id}/edit`}>
              Edit Bot Configuration
            </Link>
          </Button>
        </div>

        {/* Development Notice */}
        <Card className="mt-6 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <Calendar className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-orange-900 dark:text-orange-100">
                  Development Timeline
                </h4>
                <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                  This feature is actively being developed. Check back soon for updates, or contact support if you need this feature urgently.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ComingSoon;
