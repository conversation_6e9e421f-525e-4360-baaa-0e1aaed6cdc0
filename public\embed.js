/**
 * Webton Chatbot Embedding Script
 * 
 * This script provides an easy way for websites to embed the Webton chatbot widget.
 * It handles dynamic loading of the widget bundle and provides configuration options.
 * 
 * Usage:
 * <script src="https://cdn.webton-chatbots.com/embed.js" 
 *         data-bot-id="your-bot-id"
 *         data-primary-color="#4f46e5"
 *         data-position="bottom-right">
 * </script>
 */

(function() {
  'use strict';

  // Prevent multiple initializations
  if (window.__WEBTON_CHATBOT_LOADED__) {
    console.warn('[WebtonChatbot] Widget already loaded');
    return;
  }
  window.__WEBTON_CHATBOT_LOADED__ = true;

  // Configuration
  const WIDGET_CDN_BASE = '/dist/widget';
  const WIDGET_SCRIPT_PATH = '/chatbot-widget.js';
  const WIDGET_CSS_PATH = '/assets/css/chatbot-widget.css';

  /**
   * Get the current script element and its configuration
   */
  function getCurrentScript() {
    const scripts = document.querySelectorAll('script[src*="embed.js"]');
    return scripts[scripts.length - 1];
  }

  /**
   * Parse configuration from script attributes
   */
  function parseConfiguration(script) {
    const config = {
      // Required
      botId: script.dataset.botId || script.dataset.widgetId,
      
      // Appearance
      companyName: script.dataset.companyName,
      botName: script.dataset.botName,
      primaryColor: script.dataset.primaryColor,
      secondaryColor: script.dataset.secondaryColor,
      position: script.dataset.position || 'bottom-right',
      greetingMessage: script.dataset.greeting || script.dataset.greetingMessage,
      logo: script.dataset.logo,
      theme: script.dataset.theme || 'light',
      
      // Behavior
      autoOpen: script.dataset.autoOpen === 'true',
      showBranding: script.dataset.showBranding !== 'false',
      enableFileUpload: script.dataset.enableFileUpload === 'true',
      enableFeedback: script.dataset.enableFeedback !== 'false',
      maxMessageLength: parseInt(script.dataset.maxMessageLength) || 1000,
      
      // API endpoints
      apiEndpoint: script.dataset.apiEndpoint,
      websocketEndpoint: script.dataset.websocketEndpoint,
      
      // Security
      allowedOrigins: script.dataset.allowedOrigins ? 
        script.dataset.allowedOrigins.split(',').map(s => s.trim()) : undefined,
      
      // Debug
      debug: script.dataset.debug === 'true',
    };

    // Validate required configuration
    if (!config.botId) {
      throw new Error('Missing required attribute: data-bot-id');
    }

    return config;
  }

  /**
   * Load CSS file dynamically
   */
  function loadCSS(href) {
    return new Promise((resolve, reject) => {
      // Check if CSS is already loaded
      const existingLink = document.querySelector(`link[href="${href}"]`);
      if (existingLink) {
        resolve();
        return;
      }

      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.href = href;
      
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));
      
      document.head.appendChild(link);
    });
  }

  /**
   * Load JavaScript file dynamically
   */
  function loadScript(src) {
    return new Promise((resolve, reject) => {
      // Check if script is already loaded
      const existingScript = document.querySelector(`script[src="${src}"]`);
      if (existingScript) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = src;
      script.async = true;
      script.defer = true;
      
      script.onload = () => resolve();
      script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
      
      document.head.appendChild(script);
    });
  }

  /**
   * Wait for DOM to be ready
   */
  function waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  /**
   * Initialize the widget
   */
  async function initializeWidget() {
    try {
      // Get current script and parse configuration
      const script = getCurrentScript();
      if (!script) {
        throw new Error('Could not find embed script');
      }

      const config = parseConfiguration(script);
      
      if (config.debug) {
        console.log('[WebtonChatbot] Initializing widget with config:', config);
      }

      // Wait for DOM to be ready
      await waitForDOM();

      // Load CSS first for better UX
      try {
        await loadCSS(`${WIDGET_CDN_BASE}${WIDGET_CSS_PATH}`);
        if (config.debug) {
          console.log('[WebtonChatbot] CSS loaded successfully');
        }
      } catch (error) {
        console.warn('[WebtonChatbot] Failed to load CSS:', error);
        // Continue without CSS - widget has inline styles as fallback
      }

      // Load the widget script
      await loadScript(`${WIDGET_CDN_BASE}${WIDGET_SCRIPT_PATH}`);
      
      if (config.debug) {
        console.log('[WebtonChatbot] Widget script loaded successfully');
      }

      // Wait for WebtonChatbot to be available
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds max
      
      while (!window.WebtonChatbot && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (!window.WebtonChatbot) {
        throw new Error('WebtonChatbot API not available after loading');
      }

      // Store configuration for the widget to pick up
      window.__WEBTON_CONFIG__ = config;

      // Set up event listeners for widget events
      setupEventListeners(config);

      if (config.debug) {
        console.log('[WebtonChatbot] Widget initialized successfully');
      }

      // Dispatch initialization event
      const event = new CustomEvent('webton:embed:loaded', {
        detail: { config },
        bubbles: false,
        cancelable: false,
      });
      document.dispatchEvent(event);

    } catch (error) {
      console.error('[WebtonChatbot] Failed to initialize widget:', error);
      
      // Dispatch error event
      const errorEvent = new CustomEvent('webton:embed:error', {
        detail: { error: error.message },
        bubbles: false,
        cancelable: false,
      });
      document.dispatchEvent(errorEvent);
    }
  }

  /**
   * Set up event listeners for widget events
   */
  function setupEventListeners(config) {
    // Listen for widget events and forward them as custom events
    const eventTypes = [
      'initialized', 'opened', 'closed', 'message', 
      'error', 'conversationStarted', 'conversationEnded'
    ];

    eventTypes.forEach(eventType => {
      window.WebtonChatbot.on(eventType, (data) => {
        const customEvent = new CustomEvent(`webton:widget:${eventType}`, {
          detail: data,
          bubbles: false,
          cancelable: false,
        });
        document.dispatchEvent(customEvent);

        if (config.debug) {
          console.log(`[WebtonChatbot] Event: ${eventType}`, data);
        }
      });
    });

    // Set up analytics tracking if enabled
    if (config.enableAnalytics !== false) {
      setupAnalytics(config);
    }
  }

  /**
   * Set up basic analytics tracking
   */
  function setupAnalytics(config) {
    const analytics = {
      sessionStart: Date.now(),
      pageUrl: window.location.href,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      botId: config.botId,
    };

    // Track widget initialization
    window.WebtonChatbot.on('initialized', () => {
      analytics.widgetInitialized = Date.now();
    });

    // Track first interaction
    window.WebtonChatbot.on('opened', () => {
      if (!analytics.firstInteraction) {
        analytics.firstInteraction = Date.now();
        analytics.timeToFirstInteraction = analytics.firstInteraction - analytics.sessionStart;
      }
    });

    // Track messages
    let messageCount = 0;
    window.WebtonChatbot.on('message', (data) => {
      messageCount++;
      analytics.messageCount = messageCount;
      analytics.lastMessage = Date.now();
    });

    // Send analytics on page unload
    window.addEventListener('beforeunload', () => {
      analytics.sessionEnd = Date.now();
      analytics.sessionDuration = analytics.sessionEnd - analytics.sessionStart;

      // Send analytics data (implement based on your analytics service)
      if (config.debug) {
        console.log('[WebtonChatbot] Session analytics:', analytics);
      }
    });
  }

  /**
   * Expose public API for advanced usage
   */
  window.WebtonEmbed = {
    // Get widget API
    getWidget: () => window.WebtonChatbot,
    
    // Get configuration
    getConfig: () => window.__WEBTON_CONFIG__,
    
    // Reinitialize widget (useful for SPA navigation)
    reinitialize: initializeWidget,
    
    // Check if widget is loaded
    isLoaded: () => !!window.WebtonChatbot,
  };

  // Auto-initialize
  initializeWidget();

})();
