/**
 * Real Analytics Service
 * 
 * Provides analytics data from Supabase database for bot-specific metrics.
 * Replaces the mock analytics service with real database queries.
 */

import { supabase } from '../lib/supabase';
import { 
  AnalyticsMetrics, 
  TimeSeriesData, 
  GetAnalyticsResponse,
  AnalyticsEvent 
} from '../types/api';
import { format, parseISO, startOfDay, endOfDay, eachDayOfInterval, eachHourOfInterval, eachWeekOfInterval, eachMonthOfInterval } from 'date-fns';

/**
 * Real Analytics Service for Supabase integration
 */
export class AnalyticsService {
  /**
   * Get analytics data for a specific bot and date range
   */
  static async getAnalytics(
    botId: string,
    startDate: string,
    endDate: string,
    granularity: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<GetAnalyticsResponse> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: bot, error: botError } = await supabase
        .from('bots')
        .select('id')
        .eq('id', botId)
        .eq('user_id', user.id)
        .single();

      if (botError || !bot) {
        throw new Error('Bot not found or access denied');
      }

      // Get metrics and time series data in parallel
      const [metrics, timeSeriesData] = await Promise.all([
        this.getMetrics(botId, startDate, endDate),
        this.getTimeSeriesData(botId, startDate, endDate, granularity)
      ]);

      return {
        period: {
          start_date: startDate,
          end_date: endDate,
        },
        metrics,
        time_series: timeSeriesData,
      };
    } catch (error) {
      console.error('Error fetching analytics:', error);
      throw error;
    }
  }

  /**
   * Get aggregated metrics for the date range
   */
  private static async getMetrics(
    botId: string,
    startDate: string,
    endDate: string
  ): Promise<AnalyticsMetrics> {
    try {
      // Get conversation metrics
      const { data: conversationData, error: convError } = await supabase
        .from('conversations')
        .select('id, started_at, ended_at, user_identifier')
        .eq('bot_id', botId)
        .gte('started_at', startDate)
        .lte('started_at', endDate);

      if (convError) throw convError;

      // Get message metrics
      const { data: messageData, error: msgError } = await supabase
        .from('messages')
        .select('id, conversation_id, sender, ai_response_time, feedback_score, created_at')
        .in('conversation_id', conversationData?.map(c => c.id) || []);

      if (msgError) throw msgError;

      // Calculate metrics
      const totalConversations = conversationData?.length || 0;
      const totalMessages = messageData?.length || 0;
      const uniqueUsers = new Set(conversationData?.map(c => c.user_identifier).filter(Boolean)).size;
      
      // Calculate average response time from bot messages
      const botMessages = messageData?.filter(m => m.sender === 'bot' && m.ai_response_time) || [];
      const averageResponseTime = botMessages.length > 0 
        ? botMessages.reduce((sum, m) => sum + (m.ai_response_time || 0), 0) / botMessages.length
        : 0;

      // Calculate satisfaction score from feedback
      const messagesWithFeedback = messageData?.filter(m => m.feedback_score) || [];
      const satisfactionScore = messagesWithFeedback.length > 0
        ? messagesWithFeedback.reduce((sum, m) => sum + (m.feedback_score || 0), 0) / messagesWithFeedback.length
        : 0;

      // Calculate resolution rate (conversations that ended vs started)
      const endedConversations = conversationData?.filter(c => c.ended_at).length || 0;
      const resolutionRate = totalConversations > 0 ? endedConversations / totalConversations : 0;

      return {
        total_conversations: totalConversations,
        total_messages: totalMessages,
        unique_users: uniqueUsers,
        average_response_time: Math.round(averageResponseTime),
        satisfaction_score: Math.round(satisfactionScore * 100) / 100,
        resolution_rate: Math.round(resolutionRate * 100) / 100,
      };
    } catch (error) {
      console.error('Error calculating metrics:', error);
      throw error;
    }
  }

  /**
   * Get time series data for charts
   */
  private static async getTimeSeriesData(
    botId: string,
    startDate: string,
    endDate: string,
    granularity: 'hour' | 'day' | 'week' | 'month'
  ): Promise<TimeSeriesData[]> {
    try {
      // Get all conversations and messages for the period
      const { data: conversations, error: convError } = await supabase
        .from('conversations')
        .select('id, started_at')
        .eq('bot_id', botId)
        .gte('started_at', startDate)
        .lte('started_at', endDate)
        .order('started_at');

      if (convError) throw convError;

      const { data: messages, error: msgError } = await supabase
        .from('messages')
        .select('id, conversation_id, sender, ai_response_time, created_at')
        .in('conversation_id', conversations?.map(c => c.id) || [])
        .order('created_at');

      if (msgError) throw msgError;

      // Generate time intervals based on granularity
      const start = parseISO(startDate);
      const end = parseISO(endDate);
      let intervals: Date[] = [];

      switch (granularity) {
        case 'hour':
          intervals = eachHourOfInterval({ start, end });
          break;
        case 'day':
          intervals = eachDayOfInterval({ start, end });
          break;
        case 'week':
          intervals = eachWeekOfInterval({ start, end });
          break;
        case 'month':
          intervals = eachMonthOfInterval({ start, end });
          break;
      }

      // Group data by time intervals
      const timeSeriesData: TimeSeriesData[] = intervals.map(interval => {
        const intervalStart = startOfDay(interval);
        const intervalEnd = endOfDay(interval);

        // Count conversations started in this interval
        const conversationsInInterval = conversations?.filter(c => {
          const startedAt = parseISO(c.started_at);
          return startedAt >= intervalStart && startedAt <= intervalEnd;
        }).length || 0;

        // Count messages in this interval
        const messagesInInterval = messages?.filter(m => {
          const createdAt = parseISO(m.created_at);
          return createdAt >= intervalStart && createdAt <= intervalEnd;
        }) || [];

        // Calculate average response time for this interval
        const botMessagesInInterval = messagesInInterval.filter(m => 
          m.sender === 'bot' && m.ai_response_time
        );
        const avgResponseTime = botMessagesInInterval.length > 0
          ? botMessagesInInterval.reduce((sum, m) => sum + (m.ai_response_time || 0), 0) / botMessagesInInterval.length
          : 0;

        return {
          timestamp: interval.toISOString(),
          conversations: conversationsInInterval,
          messages: messagesInInterval.length,
          response_time: Math.round(avgResponseTime),
        };
      });

      return timeSeriesData;
    } catch (error) {
      console.error('Error generating time series data:', error);
      throw error;
    }
  }

  /**
   * Get recent analytics events
   */
  static async getRecentEvents(
    botId: string,
    limit: number = 50
  ): Promise<AnalyticsEvent[]> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: bot, error: botError } = await supabase
        .from('bots')
        .select('id')
        .eq('id', botId)
        .eq('user_id', user.id)
        .single();

      if (botError || !bot) {
        throw new Error('Bot not found or access denied');
      }

      // Note: analytics_events table might not have data yet
      // Return empty array for now, can be populated as events are tracked
      return [];
    } catch (error) {
      console.error('Error fetching recent events:', error);
      throw error;
    }
  }

  /**
   * Get conversation funnel data
   */
  static async getConversationFunnel(
    botId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    stage: string;
    count: number;
    percentage: number;
  }[]> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: conversations, error } = await supabase
        .from('conversations')
        .select('id, status, started_at, ended_at')
        .eq('bot_id', botId)
        .gte('started_at', startDate)
        .lte('started_at', endDate);

      if (error) throw error;

      const totalVisitors = conversations?.length || 0;
      const activeConversations = conversations?.filter(c => c.status === 'active').length || 0;
      const endedConversations = conversations?.filter(c => c.status === 'ended').length || 0;
      const escalatedConversations = conversations?.filter(c => c.status === 'escalated').length || 0;

      return [
        {
          stage: 'Visitors',
          count: totalVisitors,
          percentage: 100,
        },
        {
          stage: 'Started Chat',
          count: totalVisitors,
          percentage: totalVisitors > 0 ? 100 : 0,
        },
        {
          stage: 'Active Conversations',
          count: activeConversations,
          percentage: totalVisitors > 0 ? (activeConversations / totalVisitors) * 100 : 0,
        },
        {
          stage: 'Completed',
          count: endedConversations,
          percentage: totalVisitors > 0 ? (endedConversations / totalVisitors) * 100 : 0,
        },
        {
          stage: 'Escalated',
          count: escalatedConversations,
          percentage: totalVisitors > 0 ? (escalatedConversations / totalVisitors) * 100 : 0,
        },
      ];
    } catch (error) {
      console.error('Error fetching conversation funnel:', error);
      throw error;
    }
  }

  /**
   * Get satisfaction breakdown data for charts
   */
  static async getSatisfactionBreakdown(
    botId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    rating: number;
    count: number;
    percentage: number;
  }[]> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get conversations for this bot in the date range
      const { data: conversations, error: convError } = await supabase
        .from('conversations')
        .select('id')
        .eq('bot_id', botId)
        .gte('started_at', startDate)
        .lte('started_at', endDate);

      if (convError) throw convError;

      // Get messages with feedback scores
      const { data: messages, error: msgError } = await supabase
        .from('messages')
        .select('feedback_score')
        .in('conversation_id', conversations?.map(c => c.id) || [])
        .not('feedback_score', 'is', null);

      if (msgError) throw msgError;

      // Count ratings
      const ratingCounts = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      messages?.forEach(m => {
        if (m.feedback_score && m.feedback_score >= 1 && m.feedback_score <= 5) {
          ratingCounts[m.feedback_score as keyof typeof ratingCounts]++;
        }
      });

      const totalRatings = Object.values(ratingCounts).reduce((sum, count) => sum + count, 0);

      return Object.entries(ratingCounts).map(([rating, count]) => ({
        rating: parseInt(rating),
        count,
        percentage: totalRatings > 0 ? (count / totalRatings) * 100 : 0,
      }));
    } catch (error) {
      console.error('Error fetching satisfaction breakdown:', error);
      throw error;
    }
  }

  /**
   * Get topic breakdown data for charts
   */
  static async getTopicBreakdown(
    botId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    topic: string;
    count: number;
    percentage: number;
  }[]> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // For now, return empty array since we don't have topic analysis implemented
      // This can be enhanced later with AI-powered topic extraction
      return [];
    } catch (error) {
      console.error('Error fetching topic breakdown:', error);
      throw error;
    }
  }
}
