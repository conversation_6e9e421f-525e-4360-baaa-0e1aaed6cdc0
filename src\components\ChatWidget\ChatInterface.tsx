import React, { useState, useRef, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { ScrollArea } from "../ui/scroll-area";
import { Send, Paperclip, ThumbsUp, ThumbsDown } from "lucide-react";

interface Message {
  id: string;
  content: string;
  sender: "user" | "bot";
  timestamp: Date;
}

interface ChatInterfaceProps {
  botName?: string;
  botAvatar?: string;
  companyName?: string;
  accentColor?: string;
  onSendMessage?: (message: string) => void;
  onClose?: () => void;
  initialMessages?: Message[];
  isTyping?: boolean;
}

const ChatInterface = ({
  botName = "AI Assistant",
  botAvatar = "https://api.dicebear.com/7.x/avataaars/svg?seed=chatbot",
  companyName = "Company Name",
  accentColor = "#4f46e5",
  onSendMessage = () => {},
  onClose = () => {},
  initialMessages = [
    {
      id: "1",
      content: "Hello! How can I help you today?",
      sender: "bot",
      timestamp: new Date(),
    },
  ],
  isTyping = false,
}: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = () => {
    if (inputValue.trim() === "") return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages([...messages, newMessage]);
    onSendMessage(inputValue);
    setInputValue("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex flex-col h-full w-full bg-white rounded-lg shadow-lg overflow-hidden min-h-[400px]">
      {/* Chat Header */}
      <div
        className="flex items-center justify-between p-3 sm:p-4 border-b"
        style={{ backgroundColor: accentColor }}
      >
        <div className="flex items-center space-x-3">
          <Avatar>
            <AvatarImage src={botAvatar} alt={botName} />
            <AvatarFallback>{botName.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-medium text-white">{botName}</h3>
            <p className="text-xs text-white/80">{companyName}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-white hover:bg-white/20"
        >
          ×
        </Button>
      </div>

      {/* Messages Container */}
      <ScrollArea className="flex-1 p-3 sm:p-4">
        <div className="space-y-3 sm:space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
            >
              <div
                className={`max-w-[85%] sm:max-w-[80%] rounded-lg p-2 sm:p-3 ${
                  message.sender === "user"
                    ? `bg-${accentColor} text-white ml-auto`
                    : "bg-gray-100 text-gray-800"
                }`}
                style={
                  message.sender === "user"
                    ? { backgroundColor: accentColor }
                    : {}
                }
              >
                <p className="text-sm sm:text-base leading-relaxed">{message.content}</p>
                <div
                  className={`text-xs mt-1 ${message.sender === "user" ? "text-white/70" : "text-gray-500"}`}
                >
                  {message.timestamp.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </div>
              </div>
            </div>
          ))}

          {/* Typing indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-gray-100 rounded-lg p-2 sm:p-3 max-w-[85%] sm:max-w-[80%]">
                <div className="flex space-x-1">
                  <div
                    className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"
                    style={{ animationDelay: "0ms" }}
                  ></div>
                  <div
                    className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"
                    style={{ animationDelay: "150ms" }}
                  ></div>
                  <div
                    className="w-2 h-2 rounded-full bg-gray-400 animate-bounce"
                    style={{ animationDelay: "300ms" }}
                  ></div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Message Actions */}
      <div className="p-2 border-t">
        <div className="flex justify-center space-x-2 mb-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center space-x-1"
          >
            <ThumbsUp className="h-4 w-4" />
            <span>Helpful</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center space-x-1"
          >
            <ThumbsDown className="h-4 w-4" />
            <span>Not helpful</span>
          </Button>
        </div>
      </div>

      {/* Input Area */}
      <div className="p-3 sm:p-4 border-t bg-white">
        <div className="flex space-x-2">
          <Button variant="outline" size="icon" className="shrink-0 h-9 w-9 sm:h-10 sm:w-10">
            <Paperclip className="h-4 w-4" />
          </Button>
          <div className="flex-1 flex items-center rounded-md border px-2 sm:px-3 focus-within:ring-1 focus-within:ring-ring">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="flex-1 border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-sm sm:text-base"
            />
          </div>
          <Button
            onClick={handleSendMessage}
            style={{ backgroundColor: accentColor }}
            className="shrink-0 h-9 w-9 sm:h-10 sm:w-10"
            disabled={inputValue.trim() === ""}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
