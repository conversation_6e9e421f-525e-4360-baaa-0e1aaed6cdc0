import { Suspense } from "react";
import { Routes, Route } from "react-router-dom";
import Home from "./components/home";
import { WidgetConfigProvider } from "./contexts/WidgetConfigContext";
import { AuthProvider } from "./contexts/AuthContext";
import { AuthPage, ProtectedRoute, UserProfile } from "./components/Auth";
import { LoadingSpinner } from "./components/ui/loading-spinner";
import {
  BotManagement,
  BotForm,
  BotDetails,
  BotSettings,
  BotConversations,
  BotKnowledge,
  BotAnalytics
} from "./components/BotManagement";

function App() {
  return (
    <AuthProvider>
      <WidgetConfigProvider>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center">
            <LoadingSpinner size="lg" text="Loading application..." />
          </div>
        }>
          <Routes>
            {/* Public routes */}
            <Route path="/auth" element={<AuthPage />} />
            <Route path="/auth/login" element={<AuthPage initialMode="login" />} />
            <Route path="/auth/signup" element={<AuthPage initialMode="signup" />} />
            <Route path="/auth/reset" element={<AuthPage initialMode="reset" />} />

            {/* Protected routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Home />
                </ProtectedRoute>
              }
            />
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <UserProfile />
                </ProtectedRoute>
              }
            />

            {/* Bot Management routes */}
            <Route
              path="/bots"
              element={
                <ProtectedRoute>
                  <BotManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/new"
              element={
                <ProtectedRoute>
                  <BotForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/:id"
              element={
                <ProtectedRoute>
                  <BotDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/:id/edit"
              element={
                <ProtectedRoute>
                  <BotForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/:id/settings"
              element={
                <ProtectedRoute>
                  <BotSettings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/:id/conversations"
              element={
                <ProtectedRoute>
                  <BotConversations />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/:id/knowledge"
              element={
                <ProtectedRoute>
                  <BotKnowledge />
                </ProtectedRoute>
              }
            />
            <Route
              path="/bots/:id/analytics"
              element={
                <ProtectedRoute>
                  <BotAnalytics />
                </ProtectedRoute>
              }
            />
          </Routes>
          {/* Tempo routes temporarily disabled - uncomment when tempobook is configured */}
          {/* {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)} */}
        </Suspense>
      </WidgetConfigProvider>
    </AuthProvider>
  );
}

export default App;
