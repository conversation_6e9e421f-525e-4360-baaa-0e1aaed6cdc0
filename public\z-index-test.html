<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Z-Index Conflict Test - Webton Chatbot</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4f46e5;
        }
        
        .test-section h2 {
            color: #4f46e5;
            margin-top: 0;
        }
        
        .z-index-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .conflict-elements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .conflict-element {
            padding: 20px;
            border-radius: 8px;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .conflict-element:hover {
            transform: scale(1.05);
        }
        
        .modal-trigger {
            background: #ff6b6b;
            z-index: 1000;
            position: relative;
        }
        
        .dropdown-trigger {
            background: #4ecdc4;
            z-index: 999;
            position: relative;
        }
        
        .overlay-trigger {
            background: #45b7d1;
            z-index: 10000;
            position: relative;
        }
        
        .extreme-z-trigger {
            background: #f39c12;
            z-index: 2147483600;
            position: relative;
        }
        
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1050;
            justify-content: center;
            align-items: center;
        }
        
        .modal.active {
            display: flex;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            position: relative;
            z-index: 1051;
        }
        
        /* Dropdown styles */
        .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            min-width: 200px;
            display: none;
        }
        
        .dropdown.active {
            display: block;
        }
        
        .dropdown-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .dropdown-item:hover {
            background: #f5f5f5;
        }
        
        /* Overlay styles */
        .overlay {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255,255,255,0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10001;
            display: none;
        }
        
        .overlay.active {
            display: block;
        }
        
        /* Extreme z-index element */
        .extreme-element {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 2147483600;
            display: none;
        }
        
        .extreme-element.active {
            display: block;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .btn {
            padding: 10px 20px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #4338ca;
        }
        
        .btn.danger {
            background: #ef4444;
        }
        
        .btn.danger:hover {
            background: #dc2626;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Z-Index Conflict Test</h1>
        
        <div class="test-section">
            <h2>📊 Current Z-Index Information</h2>
            <div class="z-index-info" id="zIndexInfo">
                Loading z-index information...
            </div>
            <div class="controls">
                <button class="btn" onclick="refreshZIndexInfo()">Refresh Info</button>
                <button class="btn" onclick="showWidgetDebug()">Widget Debug</button>
                <button class="btn danger" onclick="clearAllElements()">Clear All</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>⚡ Conflict Test Elements</h2>
            <p>Click these elements to create z-index conflicts and test the widget's adaptive behavior:</p>
            
            <div class="conflict-elements">
                <div class="conflict-element modal-trigger" onclick="showModal()">
                    <h3>Modal Dialog</h3>
                    <p>Z-Index: 1050</p>
                    <small>Bootstrap-style modal</small>
                </div>
                
                <div class="conflict-element dropdown-trigger" onclick="showDropdown(event)">
                    <h3>Dropdown Menu</h3>
                    <p>Z-Index: 999</p>
                    <small>Standard dropdown</small>
                    <div class="dropdown" id="testDropdown">
                        <div class="dropdown-item">Option 1</div>
                        <div class="dropdown-item">Option 2</div>
                        <div class="dropdown-item">Option 3</div>
                    </div>
                </div>
                
                <div class="conflict-element overlay-trigger" onclick="showOverlay()">
                    <h3>High Z-Index Overlay</h3>
                    <p>Z-Index: 10,000</p>
                    <small>High priority overlay</small>
                </div>
                
                <div class="conflict-element extreme-z-trigger" onclick="showExtremeElement()">
                    <h3>Extreme Z-Index</h3>
                    <p>Z-Index: 2,147,483,600</p>
                    <small>Near maximum value</small>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Instructions</h2>
            <ol>
                <li><strong>Open the chat widget</strong> by clicking the chat bubble</li>
                <li><strong>Click conflict elements</strong> to create overlapping z-index scenarios</li>
                <li><strong>Observe widget behavior</strong> - it should automatically adjust its z-index</li>
                <li><strong>Check console logs</strong> for z-index conflict detection messages</li>
                <li><strong>Use "Widget Debug"</strong> button to see current z-index configuration</li>
                <li><strong>Test combinations</strong> - try multiple conflict elements at once</li>
            </ol>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal" id="testModal">
        <div class="modal-content">
            <h3>Test Modal Dialog</h3>
            <p>This modal has z-index: 1050. The chat widget should automatically detect this conflict and adjust its z-index to stay above this modal.</p>
            <button class="btn" onclick="hideModal()">Close Modal</button>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="testOverlay">
        <h3>High Z-Index Overlay</h3>
        <p>This overlay has z-index: 10,001. This tests the widget's ability to handle very high z-index values.</p>
        <button class="btn" onclick="hideOverlay()">Close Overlay</button>
    </div>

    <!-- Extreme Z-Index Element -->
    <div class="extreme-element" id="extremeElement">
        <h4>Extreme Z-Index Element</h4>
        <p>Z-Index: 2,147,483,600</p>
        <button class="btn" onclick="hideExtremeElement()">Close</button>
    </div>

    <!-- Load the widget -->
    <script src="embed.js"></script>
    
    <script>
        // Modal functions
        function showModal() {
            document.getElementById('testModal').classList.add('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        function hideModal() {
            document.getElementById('testModal').classList.remove('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        // Dropdown functions
        function showDropdown(event) {
            event.stopPropagation();
            const dropdown = document.getElementById('testDropdown');
            dropdown.classList.toggle('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        // Overlay functions
        function showOverlay() {
            document.getElementById('testOverlay').classList.add('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        function hideOverlay() {
            document.getElementById('testOverlay').classList.remove('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        // Extreme element functions
        function showExtremeElement() {
            document.getElementById('extremeElement').classList.add('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        function hideExtremeElement() {
            document.getElementById('extremeElement').classList.remove('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        // Clear all elements
        function clearAllElements() {
            hideModal();
            hideOverlay();
            hideExtremeElement();
            document.getElementById('testDropdown').classList.remove('active');
            setTimeout(refreshZIndexInfo, 100);
        }
        
        // Z-index information functions
        function refreshZIndexInfo() {
            const info = document.getElementById('zIndexInfo');
            
            // Get all elements with z-index
            const elements = document.querySelectorAll('*');
            const zIndexElements = [];
            
            elements.forEach(el => {
                const style = window.getComputedStyle(el);
                const zIndex = style.zIndex;
                if (zIndex !== 'auto' && zIndex !== 'initial') {
                    const zIndexNum = parseInt(zIndex);
                    if (!isNaN(zIndexNum) && zIndexNum > 0) {
                        zIndexElements.push({
                            element: el.tagName + (el.className ? '.' + el.className.split(' ')[0] : ''),
                            zIndex: zIndexNum
                        });
                    }
                }
            });
            
            // Sort by z-index
            zIndexElements.sort((a, b) => b.zIndex - a.zIndex);
            
            let infoHTML = '<strong>Current Z-Index Stack (highest first):</strong><br>';
            zIndexElements.slice(0, 10).forEach(item => {
                infoHTML += `${item.element}: ${item.zIndex}<br>`;
            });
            
            if (zIndexElements.length > 10) {
                infoHTML += `... and ${zIndexElements.length - 10} more elements<br>`;
            }
            
            infoHTML += `<br><strong>Total elements with z-index:</strong> ${zIndexElements.length}`;
            infoHTML += `<br><strong>Highest z-index detected:</strong> ${zIndexElements.length > 0 ? zIndexElements[0].zIndex : 'None'}`;
            
            info.innerHTML = infoHTML;
        }
        
        function showWidgetDebug() {
            if (window.WebtonChatbot && window.WebtonChatbot.getZIndexInfo) {
                const debugInfo = window.WebtonChatbot.getZIndexInfo();
                console.log('Widget Z-Index Debug Info:', debugInfo);
                alert('Widget debug info logged to console. Check developer tools.');
            } else {
                alert('Widget not initialized or debug method not available.');
            }
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            document.getElementById('testDropdown').classList.remove('active');
        });
        
        // Initialize z-index info
        refreshZIndexInfo();
        
        // Listen for widget z-index events
        document.addEventListener('webton:zIndexInitialized', function(event) {
            console.log('Widget z-index initialized:', event.detail);
            refreshZIndexInfo();
        });
        
        document.addEventListener('webton:zIndexConflictResolved', function(event) {
            console.log('Widget z-index conflict resolved:', event.detail);
            refreshZIndexInfo();
        });
        
        // Initialize the widget
        window.addEventListener('load', function() {
            if (window.WebtonChatbot) {
                window.WebtonChatbot.init({
                    botId: 'z-index-test',
                    apiEndpoint: 'https://api.webton-chatbots.com/v1',
                    position: 'bottom-right',
                    debug: true,
                    theme: {
                        primaryColor: '#4f46e5',
                        secondaryColor: '#ffffff'
                    },
                    branding: {
                        companyName: 'Z-Index Test',
                        logo: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zindex'
                    }
                });
            }
        });
    </script>
</body>
</html>
