/**
 * AuthContext Definition
 * 
 * Separated from AuthContext.tsx to comply with Vite Fast Refresh requirements.
 * This file only contains the context definition and types.
 */

import { createContext } from 'react';
import type { User, Session, AuthError } from "@supabase/supabase-js";
import type { UserRole } from "../lib/supabase";

/**
 * User profile interface matching the database schema
 */
export interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  company_name: string | null;
  role: UserRole;
  avatar_url: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Authentication context interface
 */
export interface AuthContextType {
  // State
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  loading: boolean;

  // Actions
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signUp: (email: string, password: string, fullName: string, companyName?: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: Error | null }>;

  // Role checks
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;

  // Permissions
  canManageUsers: boolean;
  canManageBots: boolean;
  canViewAnalytics: boolean;
  canEditSettings: boolean;
  canExportData: boolean;
}

/**
 * Authentication context
 */
export const AuthContext = createContext<AuthContextType | undefined>(undefined);
