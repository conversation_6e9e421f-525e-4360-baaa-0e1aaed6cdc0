/**
 * Widget Styles
 * 
 * Isolated CSS styles for the embeddable chat widget.
 * All styles are scoped to prevent conflicts with host website styles.
 */

/* Reset and base styles for widget container */
.webton-chatbot-container,
.webton-chatbot-container * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: inherit !important;
  text-decoration: none !important;
  list-style: none !important;
  vertical-align: baseline !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Widget container positioning */
.webton-chatbot-container {
  position: fixed !important;
  z-index: 2147483647 !important; /* Maximum z-index */
  bottom: 20px !important;
  font-family: "Inter", -apple-system, <PERSON>linkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #333333 !important;
  pointer-events: auto !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* Position variants */
.webton-chatbot-container[data-position="bottom-right"] {
  right: 20px !important;
}

.webton-chatbot-container[data-position="bottom-left"] {
  left: 20px !important;
}

/* Widget animations */
@keyframes webton-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes webton-fade-out {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

@keyframes webton-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes webton-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

/* Widget bubble styles */
.webton-chat-bubble {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
  animation: webton-fade-in 0.3s ease-out !important;
}

.webton-chat-bubble:hover {
  transform: scale(1.05) !important;
  animation: webton-pulse 2s infinite !important;
}

.webton-chat-bubble:active {
  transform: scale(0.95) !important;
}

/* Widget window styles */
.webton-chat-window {
  width: 350px !important;
  height: 500px !important;
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  animation: webton-fade-in 0.3s ease-out !important;
}

/* Header styles */
.webton-chat-header {
  padding: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.webton-chat-header-info {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.webton-chat-avatar {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  object-fit: cover !important;
}

.webton-chat-title {
  font-weight: 600 !important;
  font-size: 16px !important;
  color: inherit !important;
}

.webton-chat-status {
  font-size: 12px !important;
  opacity: 0.8 !important;
  color: inherit !important;
}

.webton-chat-close {
  background: none !important;
  border: none !important;
  color: inherit !important;
  font-size: 20px !important;
  cursor: pointer !important;
  padding: 4px !important;
  border-radius: 4px !important;
  transition: background-color 0.2s ease !important;
}

.webton-chat-close:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Messages area styles */
.webton-chat-messages {
  flex: 1 !important;
  padding: 16px !important;
  overflow-y: auto !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  background: #ffffff !important;
}

.webton-chat-messages::-webkit-scrollbar {
  width: 6px !important;
}

.webton-chat-messages::-webkit-scrollbar-track {
  background: #f1f5f9 !important;
  border-radius: 3px !important;
}

.webton-chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e1 !important;
  border-radius: 3px !important;
}

.webton-chat-messages::-webkit-scrollbar-thumb:hover {
  background: #94a3b8 !important;
}

/* Message styles */
.webton-message {
  display: flex !important;
  margin-bottom: 8px !important;
}

.webton-message-user {
  justify-content: flex-end !important;
}

.webton-message-bot {
  justify-content: flex-start !important;
}

.webton-message-content {
  max-width: 80% !important;
  padding: 8px 12px !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  word-wrap: break-word !important;
  animation: webton-fade-in 0.2s ease-out !important;
}

.webton-message-user .webton-message-content {
  background: #4f46e5 !important;
  color: #ffffff !important;
}

.webton-message-bot .webton-message-content {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

.webton-message-timestamp {
  font-size: 11px !important;
  opacity: 0.7 !important;
  margin-top: 4px !important;
  color: inherit !important;
}

/* Typing indicator */
.webton-typing-indicator {
  display: flex !important;
  justify-content: flex-start !important;
}

.webton-typing-content {
  padding: 8px 12px !important;
  border-radius: 12px !important;
  background: #f3f4f6 !important;
  color: #6b7280 !important;
  font-size: 14px !important;
  animation: webton-pulse 1.5s infinite !important;
}

/* Input area styles */
.webton-chat-input-area {
  padding: 16px !important;
  border-top: 1px solid #e5e7eb !important;
  display: flex !important;
  gap: 8px !important;
  align-items: center !important;
  background: #ffffff !important;
  min-height: 60px !important;
}

.webton-chat-input {
  flex: 1 !important;
  padding: 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 16px !important; /* Prevents zoom on iOS */
  font-family: inherit !important;
  outline: none !important;
  resize: none !important;
  background: #ffffff !important;
  color: #374151 !important;
  line-height: 1.5 !important;
  transition: border-color 0.2s ease !important;
  min-height: 44px !important; /* Touch-friendly minimum */
}

.webton-chat-input:focus {
  border-color: #4f46e5 !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
}

.webton-chat-input::placeholder {
  color: #9ca3af !important;
}

.webton-chat-send-button {
  background: #4f46e5 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 12px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 44px !important; /* Touch-friendly minimum */
  height: 44px !important; /* Touch-friendly minimum */
  transition: all 0.2s ease !important;
}

.webton-chat-send-button:hover:not(:disabled) {
  background: #4338ca !important;
  transform: translateY(-1px) !important;
}

.webton-chat-send-button:active:not(:disabled) {
  transform: translateY(0) !important;
}

.webton-chat-send-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Responsive design */
/* Mobile phones (portrait) */
@media (max-width: 480px) {
  .webton-chatbot-container {
    bottom: 10px !important;
    right: 10px !important;
    left: 10px !important;
  }

  .webton-chat-window {
    width: 100% !important;
    max-width: calc(100vw - 20px) !important;
    height: 75vh !important;
    max-height: 600px !important;
    min-height: 400px !important;
  }

  .webton-chat-bubble {
    width: 56px !important;
    height: 56px !important;
  }

  .webton-chat-header {
    padding: 12px !important;
  }

  .webton-chat-messages {
    padding: 12px !important;
  }

  .webton-chat-input-area {
    padding: 12px !important;
  }
}

/* Mobile phones (landscape) and small tablets */
@media (max-width: 768px) and (orientation: landscape) {
  .webton-chat-window {
    height: 85vh !important;
    max-height: 450px !important;
  }
}

/* Tablets (portrait) */
@media (min-width: 481px) and (max-width: 768px) {
  .webton-chatbot-container {
    bottom: 15px !important;
    right: 15px !important;
  }

  .webton-chat-window {
    width: 380px !important;
    max-width: calc(100vw - 30px) !important;
    height: 520px !important;
  }
}

/* Small screens with limited height */
@media (max-height: 600px) {
  .webton-chat-window {
    height: 90vh !important;
    max-height: none !important;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .webton-chatbot-container[data-theme="auto"] .webton-chat-window {
    background: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
  }
  
  .webton-chatbot-container[data-theme="auto"] .webton-chat-messages {
    background: #1f2937 !important;
  }
  
  .webton-chatbot-container[data-theme="auto"] .webton-chat-input-area {
    background: #1f2937 !important;
    border-color: #374151 !important;
  }
  
  .webton-chatbot-container[data-theme="auto"] .webton-chat-input {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
  }
  
  .webton-chatbot-container[data-theme="auto"] .webton-message-bot .webton-message-content {
    background: #374151 !important;
    color: #f9fafb !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .webton-chat-window {
    border: 2px solid #000000 !important;
  }
  
  .webton-chat-input {
    border: 2px solid #000000 !important;
  }
  
  .webton-chat-send-button {
    border: 2px solid #000000 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .webton-chatbot-container *,
  .webton-chatbot-container *::before,
  .webton-chatbot-container *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
