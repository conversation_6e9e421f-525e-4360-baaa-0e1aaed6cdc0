import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MessageSquare,
  Settings,
  BarChart3,
  FileText,
  Users,
  LogOut,
  User,
  Bot as BotIcon,
} from "lucide-react";
import { useAuth } from "../hooks/useAuth";
import WidgetPreview from "./WidgetPreview";
import { AnalyticsDashboard } from "./Dashboard/AnalyticsDashboard";
import { KnowledgeBaseManager } from "./KnowledgeBase";

const Home = () => {
  const { user, profile, signOut } = useAuth();

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      <header className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col md:flex-row items-center gap-8"><img className="w-64" src="/base_logo_transparent_background.png" alt="Webton Logo" />
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Chatbot Widget Dashboard
              </h1>
              <p className="mt-1 text-muted-foreground">
                Manage and customize your AI-powered chatbot widget
              </p>
            </div></div>

          <div className="mt-4 md:mt-0 flex items-center gap-4">
            <Button asChild variant="outline">
              <Link to="/bots" className="flex items-center gap-2">
                <BotIcon className="h-4 w-4" />
                Manage Bots
              </Link>
            </Button>

            <Badge variant="secondary" className="text-xs">
              Active Plan: Professional
            </Badge>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={profile?.avatar_url || undefined} />
                    <AvatarFallback>
                      {profile?.full_name ? getInitials(profile.full_name) : 'U'}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {profile?.full_name || 'User'}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user?.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs defaultValue="preview" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="preview">
                <MessageSquare className="mr-2 h-4 w-4" />
                Widget Preview
              </TabsTrigger>
              <TabsTrigger value="analytics">
                <BarChart3 className="mr-2 h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="knowledge">
                <FileText className="mr-2 h-4 w-4" />
                Knowledge Base
              </TabsTrigger>
              <TabsTrigger value="conversations">
                <Users className="mr-2 h-4 w-4" />
                Conversations
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Widget Preview</CardTitle>
                  <CardDescription>
                    See how your chatbot will appear on your website. Click the
                    chat bubble to interact with it.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <WidgetPreview />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <AnalyticsDashboard botId="demo-bot-123" />
            </TabsContent>

            <TabsContent value="knowledge" className="space-y-4">
              <KnowledgeBaseManager botId="demo-bot-123" />
            </TabsContent>

            <TabsContent value="conversations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Conversations</CardTitle>
                  <CardDescription>
                    Review recent interactions with your chatbot.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md divide-y">
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className="p-4 hover:bg-muted/50 cursor-pointer"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">
                              Visitor #{i}0{i}5
                            </p>
                            <p className="text-sm text-muted-foreground mt-1">
                              "How do I reset my password?"
                            </p>
                          </div>
                          <Badge variant="outline">
                            {i} hour{i !== 1 ? "s" : ""} ago
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Widget Configuration</CardTitle>
              <CardDescription>
                Customize how your chatbot looks and behaves.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Widget Name</label>
                  <input
                    type="text"
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    defaultValue="Support Assistant"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Welcome Message</label>
                  <textarea
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    rows={3}
                    defaultValue="Hi there! 👋 How can I help you today?"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Primary Color</label>
                  <div className="flex items-center mt-1 gap-2">
                    <input
                      type="color"
                      className="w-10 h-10 rounded cursor-pointer"
                      defaultValue="#7C3AED"
                    />
                    <input
                      type="text"
                      className="flex-1 px-3 py-2 border rounded-md"
                      defaultValue="#7C3AED"
                    />
                  </div>
                </div>
                <Button className="w-full">Save Changes</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Installation</CardTitle>
              <CardDescription>
                Add this code to your website to display the chatbot widget.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md">
                <code className="text-xs break-all">
                  {`<script src="https://chatbot-widget.example.com/widget.js" data-widget-id="YOUR_WIDGET_ID" async></script>`}
                </code>
              </div>
              <Button variant="outline" className="w-full mt-4">
                Copy Code
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Home;
