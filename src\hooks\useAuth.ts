/**
 * useAuth Hook
 * 
 * Custom hook for accessing authentication context.
 * Separated from AuthContext.tsx to comply with Vite Fast Refresh requirements.
 */

import { useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';

/**
 * Custom hook to access the authentication context.
 * Must be used within an AuthProvider.
 * 
 * @returns AuthContextType - The authentication context value
 * @throws Error if used outside of AuthProvider
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
