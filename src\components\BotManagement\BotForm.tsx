/**
 * Bot Form Component
 * 
 * Create/edit form with validation and bot configuration options.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ArrowLeft, Bot as BotIcon, Save } from 'lucide-react';
import { createBot, updateBot, getBot } from '../../services/botService';
import type { Database } from '../../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];
type BotInsert = Database['public']['Tables']['bots']['Insert'];

// Form validation schema
const botFormSchema = z.object({
  name: z.string().min(1, 'Bot name is required').max(100, 'Name must be less than 100 characters'),
  type: z.enum(['faq', 'lead_generation'], {
    required_error: 'Please select a bot type',
  }),
  status: z.enum(['active', 'inactive']).default('active'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  welcomeMessage: z.string().max(1000, 'Welcome message must be less than 1000 characters').optional(),
  fallbackMessage: z.string().max(1000, 'Fallback message must be less than 1000 characters').optional(),
});

type BotFormData = z.infer<typeof botFormSchema>;

const BotForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditing = Boolean(id);
  
  const [loading, setLoading] = useState(isEditing);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<BotFormData>({
    resolver: zodResolver(botFormSchema),
    defaultValues: {
      status: 'active',
      description: '',
      welcomeMessage: 'Hello! How can I help you today?',
      fallbackMessage: "I'm sorry, I didn't understand that. Could you please rephrase your question?",
    },
  });

  const watchedType = watch('type');

  // Load bot data for editing
  useEffect(() => {
    if (isEditing && id) {
      loadBot(id);
    }
  }, [isEditing, id]);

  const loadBot = async (botId: string) => {
    try {
      setLoading(true);
      setError(null);
      const bot = await getBot(botId);
      
      // Populate form with bot data
      setValue('name', bot.name);
      setValue('type', bot.type);
      setValue('status', bot.status);
      
      // Extract config values if they exist
      const config = bot.config as any;
      if (config) {
        setValue('description', config.description || '');
        setValue('welcomeMessage', config.welcomeMessage || 'Hello! How can I help you today?');
        setValue('fallbackMessage', config.fallbackMessage || "I'm sorry, I didn't understand that. Could you please rephrase your question?");
      }
    } catch (err) {
      console.error('Error loading bot:', err);
      setError('Failed to load bot data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: BotFormData) => {
    try {
      setSaving(true);
      setError(null);

      // Prepare bot data
      const botData: Partial<BotInsert> = {
        name: data.name,
        type: data.type,
        status: data.status,
        config: {
          description: data.description,
          welcomeMessage: data.welcomeMessage,
          fallbackMessage: data.fallbackMessage,
        },
      };

      if (isEditing && id) {
        await updateBot(id, botData);
      } else {
        await createBot(botData as BotInsert);
      }

      // Navigate back to bot management
      navigate('/bots');
    } catch (err) {
      console.error('Error saving bot:', err);
      setError('Failed to save bot. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading bot..." />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/bots')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bots
          </Button>
        </div>
        
        <div className="flex items-center gap-2">
          <BotIcon className="h-8 w-8" />
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {isEditing ? 'Edit Bot' : 'Create New Bot'}
            </h1>
            <p className="mt-1 text-muted-foreground">
              {isEditing ? 'Update your bot configuration' : 'Configure your new AI chatbot'}
            </p>
          </div>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-center">
              <p>{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Bot Configuration</CardTitle>
            <CardDescription>
              Set up your bot's basic information and behavior
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Bot Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Bot Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter bot name"
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            {/* Bot Type */}
            <div className="space-y-2">
              <Label htmlFor="type">Bot Type *</Label>
              <Select
                value={watchedType}
                onValueChange={(value) => setValue('type', value as 'faq' | 'lead_generation')}
              >
                <SelectTrigger className={errors.type ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select bot type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="faq">FAQ Bot</SelectItem>
                  <SelectItem value="lead_generation">Lead Generation Bot</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-destructive">{errors.type.message}</p>
              )}
            </div>

            {/* Bot Status */}
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={watch('status')}
                onValueChange={(value) => setValue('status', value as 'active' | 'inactive')}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Describe what this bot does..."
                rows={3}
                className={errors.description ? 'border-destructive' : ''}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
            </div>

            {/* Welcome Message */}
            <div className="space-y-2">
              <Label htmlFor="welcomeMessage">Welcome Message</Label>
              <Textarea
                id="welcomeMessage"
                {...register('welcomeMessage')}
                placeholder="Hello! How can I help you today?"
                rows={2}
                className={errors.welcomeMessage ? 'border-destructive' : ''}
              />
              {errors.welcomeMessage && (
                <p className="text-sm text-destructive">{errors.welcomeMessage.message}</p>
              )}
            </div>

            {/* Fallback Message */}
            <div className="space-y-2">
              <Label htmlFor="fallbackMessage">Fallback Message</Label>
              <Textarea
                id="fallbackMessage"
                {...register('fallbackMessage')}
                placeholder="I'm sorry, I didn't understand that..."
                rows={2}
                className={errors.fallbackMessage ? 'border-destructive' : ''}
              />
              {errors.fallbackMessage && (
                <p className="text-sm text-destructive">{errors.fallbackMessage.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex gap-4 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate('/bots')}
            disabled={saving}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={!isValid || saving}
            className="flex items-center gap-2"
          >
            {saving ? (
              <LoadingSpinner size="sm" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            {saving ? 'Saving...' : (isEditing ? 'Update Bot' : 'Create Bot')}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BotForm;
