# Authentication System Documentation

## Overview

The Webton AI Chatbots Platform implements a comprehensive authentication system using Supabase Auth with role-based access control (RBAC). This document outlines the authentication architecture, implementation details, and usage patterns.

## Architecture

### Components

1. **Supabase Client** (`src/lib/supabase.ts`)
   - Centralized Supabase configuration
   - Helper functions for common auth operations
   - Role-based permission utilities

2. **Auth Context** (`src/contexts/AuthContext.tsx`)
   - Global authentication state management
   - Authentication actions (login, signup, logout)
   - Permission checking utilities

3. **Auth Components** (`src/components/Auth/`)
   - LoginForm - User login interface
   - SignupForm - User registration interface
   - ResetPasswordForm - Password reset interface
   - AuthPage - Main authentication page with tab switching
   - ProtectedRoute - Route protection wrapper
   - UserProfile - User profile management

## User Roles

### Role Hierarchy

1. **Admin** (`admin`)
   - Full system access
   - User management capabilities
   - All bot and analytics permissions

2. **Client** (`client`)
   - Bot creation and management
   - Analytics access
   - Settings configuration
   - Data export capabilities

3. **Viewer** (`viewer`)
   - Read-only analytics access
   - Limited dashboard viewing

### Permission Matrix

| Feature | Admin | Client | Viewer |
|---------|-------|--------|--------|
| Manage Users | ✅ | ❌ | ❌ |
| Manage Bots | ✅ | ✅ | ❌ |
| View Analytics | ✅ | ✅ | ✅ |
| Edit Settings | ✅ | ✅ | ❌ |
| Export Data | ✅ | ✅ | ❌ |

## Implementation Details

### Environment Configuration

Required environment variables in `.env`:

```bash
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### Database Schema

The authentication system expects the following user table structure:

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT NOT NULL,
  full_name TEXT,
  company_name TEXT,
  role TEXT NOT NULL DEFAULT 'client' CHECK (role IN ('admin', 'client', 'viewer')),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Auth Context Usage

```typescript
import { useAuth } from '../hooks/useAuth';

function MyComponent() {
  const { 
    user, 
    profile, 
    loading, 
    signIn, 
    signOut, 
    canManageBots 
  } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (!user) return <AuthPage />;

  return (
    <div>
      <h1>Welcome, {profile?.full_name}</h1>
      {canManageBots && <BotManagement />}
    </div>
  );
}
```

### Protected Routes

```typescript
import { ProtectedRoute, AdminRoute, ClientRoute } from '../components/Auth';

// Basic authentication required
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>

// Admin-only access
<AdminRoute>
  <UserManagement />
</AdminRoute>

// Client or Admin access
<ClientRoute>
  <BotConfiguration />
</ClientRoute>

// Custom role requirements
<ProtectedRoute requiredRoles={['admin', 'client']}>
  <AnalyticsDashboard />
</ProtectedRoute>
```

### Permission Checking

```typescript
import { useAuth } from '../hooks/useAuth';

function FeatureComponent() {
  const { hasRole, hasAnyRole, canManageUsers } = useAuth();

  return (
    <div>
      {hasRole('admin') && <AdminPanel />}
      {hasAnyRole(['admin', 'client']) && <BotControls />}
      {canManageUsers && <UserManagement />}
    </div>
  );
}
```

## Authentication Flow

### Registration Flow

1. User fills out signup form with email, password, full name, and optional company name
2. Supabase creates auth user with default 'client' role in metadata
3. User profile is created in the users table
4. Email verification is sent (if enabled in Supabase)
5. User can login after email verification

### Login Flow

1. User provides email and password
2. Supabase authenticates and returns session
3. User profile is fetched from database
4. Auth context is updated with user data
5. User is redirected to dashboard

### Session Management

- Sessions are automatically persisted in localStorage
- Tokens are automatically refreshed before expiration
- Session state is synchronized across browser tabs
- Users are automatically logged out on token expiration

## Security Considerations

### Client-Side Security

- All sensitive operations require server-side validation
- Role checks are performed on both client and server
- Supabase RLS (Row Level Security) policies should be implemented
- API keys are properly scoped (anon key for client-side)

### Best Practices

1. **Never trust client-side role checks** - Always validate on server
2. **Use RLS policies** - Implement database-level security
3. **Validate permissions** - Check permissions before sensitive operations
4. **Secure storage** - Use secure storage for sensitive data
5. **Regular audits** - Monitor authentication logs and user activities

## Testing

### Unit Tests

Test authentication components and utilities:

```typescript
// Test auth context
describe('AuthContext', () => {
  it('should handle login correctly', async () => {
    // Test implementation
  });
});

// Test protected routes
describe('ProtectedRoute', () => {
  it('should redirect unauthenticated users', () => {
    // Test implementation
  });
});
```

### Integration Tests

Test complete authentication flows:

```typescript
// Test login flow
describe('Login Flow', () => {
  it('should authenticate user and redirect to dashboard', async () => {
    // Playwright test implementation
  });
});
```

## Troubleshooting

### Common Issues

1. **Environment Variables Not Set**
   - Ensure `.env` file exists with correct Supabase credentials
   - Restart development server after adding environment variables

2. **User Profile Not Created**
   - Check database permissions and RLS policies
   - Verify user table schema matches expected structure

3. **Permission Denied Errors**
   - Verify user role is correctly set in database
   - Check RLS policies allow the operation

4. **Session Not Persisting**
   - Check localStorage permissions
   - Verify Supabase client configuration

### Debug Mode

Enable debug logging by setting:

```typescript
// In development
if (import.meta.env.DEV) {
  console.log('Auth state:', { user, profile, loading });
}
```

## Future Enhancements

- [ ] Social authentication (Google, GitHub)
- [ ] Two-factor authentication (2FA)
- [ ] Advanced role management
- [ ] Audit logging
- [ ] Session management dashboard
- [ ] API key management for programmatic access
