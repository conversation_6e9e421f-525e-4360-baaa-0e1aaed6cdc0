/**
 * Bot Card Component
 * 
 * Individual bot display component with status, stats, and quick actions.
 */

import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Bot as BotIcon,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Power,
  MessageSquare,
  Users,
  Calendar,
} from 'lucide-react';
import { deleteBot, updateBot } from '../../services/botService';
import type { Database } from '../../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];

interface BotCardProps {
  bot: Bot;
  onDeleted: (botId: string) => void;
}

const BotCard: React.FC<BotCardProps> = ({ bot, onDeleted }) => {
  const navigate = useNavigate();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      await deleteBot(bot.id);
      onDeleted(bot.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting bot:', error);
      // TODO: Show error toast
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleStatus = async () => {
    try {
      setIsTogglingStatus(true);
      const newStatus = bot.status === 'active' ? 'inactive' : 'active';
      await updateBot(bot.id, { status: newStatus });
      // TODO: Update local state or refetch data
      window.location.reload(); // Temporary solution
    } catch (error) {
      console.error('Error toggling bot status:', error);
      // TODO: Show error toast
    } finally {
      setIsTogglingStatus(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getBotTypeLabel = (type: string) => {
    switch (type) {
      case 'faq':
        return 'FAQ Bot';
      case 'lead_generation':
        return 'Lead Generation';
      default:
        return type;
    }
  };

  const getBotTypeColor = (type: string) => {
    switch (type) {
      case 'faq':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'lead_generation':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <>
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <BotIcon className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className="text-lg">{bot.name}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge 
                    variant="secondary" 
                    className={getBotTypeColor(bot.type)}
                  >
                    {getBotTypeLabel(bot.type)}
                  </Badge>
                  <Badge 
                    variant={bot.status === 'active' ? 'default' : 'secondary'}
                    className={
                      bot.status === 'active' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                    }
                  >
                    {bot.status}
                  </Badge>
                </div>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link to={`/bots/${bot.id}`} className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    View Details
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to={`/bots/${bot.id}/edit`} className="flex items-center gap-2">
                    <Edit className="h-4 w-4" />
                    Edit Bot
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={handleToggleStatus}
                  disabled={isTogglingStatus}
                  className="flex items-center gap-2"
                >
                  <Power className="h-4 w-4" />
                  {bot.status === 'active' ? 'Deactivate' : 'Activate'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => setShowDeleteDialog(true)}
                  className="flex items-center gap-2 text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Bot
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent>
          {/* Bot Stats */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <MessageSquare className="h-4 w-4 text-muted-foreground mx-auto mb-1" />
              <div className="text-sm font-medium">0</div>
              <div className="text-xs text-muted-foreground">Messages</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <Users className="h-4 w-4 text-muted-foreground mx-auto mb-1" />
              <div className="text-sm font-medium">0</div>
              <div className="text-xs text-muted-foreground">Conversations</div>
            </div>
          </div>

          {/* Created Date */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
            <Calendar className="h-4 w-4" />
            Created {formatDate(bot.created_at)}
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button asChild variant="outline" size="sm" className="flex-1">
              <Link to={`/bots/${bot.id}`}>
                View Details
              </Link>
            </Button>
            <Button asChild size="sm" className="flex-1">
              <Link to={`/bots/${bot.id}/edit`}>
                Edit
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Bot</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{bot.name}"? This action cannot be undone.
              All conversations and data associated with this bot will be permanently deleted.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete Bot'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default BotCard;
