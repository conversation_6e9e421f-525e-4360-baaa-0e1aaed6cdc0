/**
 * Knowledge Service
 * 
 * Handles document management operations for bot knowledge bases.
 * Integrates with Supabase database and storage for file management.
 */

import { supabase } from '../lib/supabase';
import type { Database } from '../types/supabase';

type KnowledgeDocument = Database['public']['Tables']['knowledge_documents']['Row'];
type KnowledgeDocumentInsert = Database['public']['Tables']['knowledge_documents']['Insert'];
type KnowledgeDocumentUpdate = Database['public']['Tables']['knowledge_documents']['Update'];

export interface FileUploadData {
  file: File;
  title: string;
}

export interface DocumentSearchOptions {
  query?: string;
  status?: string[];
  fileTypes?: string[];
  sortBy?: 'created_at' | 'updated_at' | 'title' | 'file_size';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Knowledge Service for document management
 */
export class KnowledgeService {
  /**
   * Get all documents for a specific bot
   */
  static async getDocuments(
    botId: string,
    options: DocumentSearchOptions = {}
  ): Promise<KnowledgeDocument[]> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: bot, error: botError } = await supabase
        .from('bots')
        .select('id')
        .eq('id', botId)
        .eq('user_id', user.id)
        .single();

      if (botError || !bot) {
        throw new Error('Bot not found or access denied');
      }

      // Build query
      let query = supabase
        .from('knowledge_documents')
        .select('*')
        .eq('bot_id', botId);

      // Apply filters
      if (options.query) {
        query = query.or(`title.ilike.%${options.query}%,content.ilike.%${options.query}%`);
      }

      if (options.status && options.status.length > 0) {
        query = query.in('processing_status', options.status);
      }

      if (options.fileTypes && options.fileTypes.length > 0) {
        query = query.in('file_type', options.fileTypes);
      }

      // Apply sorting
      const sortBy = options.sortBy || 'created_at';
      const sortOrder = options.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching documents:', error);
      throw error;
    }
  }

  /**
   * Get a single document by ID
   */
  static async getDocument(documentId: string): Promise<KnowledgeDocument> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get document with bot ownership verification
      const { data, error } = await supabase
        .from('knowledge_documents')
        .select(`
          *,
          bots!inner(user_id)
        `)
        .eq('id', documentId)
        .eq('bots.user_id', user.id)
        .single();

      if (error) throw error;
      if (!data) throw new Error('Document not found');

      return data;
    } catch (error) {
      console.error('Error fetching document:', error);
      throw error;
    }
  }

  /**
   * Upload a new document
   */
  static async uploadDocument(
    botId: string,
    uploadData: FileUploadData
  ): Promise<KnowledgeDocument> {
    try {
      // Verify user owns this bot
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: bot, error: botError } = await supabase
        .from('bots')
        .select('id')
        .eq('id', botId)
        .eq('user_id', user.id)
        .single();

      if (botError || !bot) {
        throw new Error('Bot not found or access denied');
      }

      // Upload file to Supabase Storage
      const fileExt = uploadData.file.name.split('.').pop();
      const fileName = `${botId}/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`;
      
      const { data: uploadResult, error: uploadError } = await supabase.storage
        .from('knowledge-documents')
        .upload(fileName, uploadData.file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('knowledge-documents')
        .getPublicUrl(fileName);

      // Create document record
      const documentData: KnowledgeDocumentInsert = {
        bot_id: botId,
        title: uploadData.title,
        file_path: uploadResult.path,
        file_type: uploadData.file.type,
        file_size: uploadData.file.size,
        processing_status: 'processing',
        embedding_status: 'pending',
      };

      const { data: document, error: insertError } = await supabase
        .from('knowledge_documents')
        .insert(documentData)
        .select()
        .single();

      if (insertError) throw insertError;

      // TODO: Trigger document processing (text extraction, embedding generation)
      // This would typically be handled by a background job or webhook

      return document;
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  }

  /**
   * Update document metadata
   */
  static async updateDocument(
    documentId: string,
    updates: { title?: string; content?: string }
  ): Promise<KnowledgeDocument> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Update with ownership verification
      const { data, error } = await supabase
        .from('knowledge_documents')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', documentId)
        .eq('bot_id', await this.getBotIdForDocument(documentId))
        .select()
        .single();

      if (error) throw error;
      if (!data) throw new Error('Document not found or access denied');

      return data;
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  }

  /**
   * Delete a document
   */
  static async deleteDocument(documentId: string): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get document to verify ownership and get file path
      const document = await this.getDocument(documentId);

      // Delete file from storage if it exists
      if (document.file_path) {
        const { error: storageError } = await supabase.storage
          .from('knowledge-documents')
          .remove([document.file_path]);

        if (storageError) {
          console.warn('Error deleting file from storage:', storageError);
          // Continue with database deletion even if storage deletion fails
        }
      }

      // Delete document record
      const { error } = await supabase
        .from('knowledge_documents')
        .delete()
        .eq('id', documentId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  }

  /**
   * Get document download URL
   */
  static async getDownloadUrl(documentId: string): Promise<string> {
    try {
      const document = await this.getDocument(documentId);
      
      if (!document.file_path) {
        throw new Error('Document has no associated file');
      }

      // Create signed URL for download
      const { data, error } = await supabase.storage
        .from('knowledge-documents')
        .createSignedUrl(document.file_path, 3600); // 1 hour expiry

      if (error) throw error;
      if (!data?.signedUrl) throw new Error('Failed to generate download URL');

      return data.signedUrl;
    } catch (error) {
      console.error('Error generating download URL:', error);
      throw error;
    }
  }

  /**
   * Get bot ID for a document (helper method)
   */
  private static async getBotIdForDocument(documentId: string): Promise<string> {
    const { data, error } = await supabase
      .from('knowledge_documents')
      .select('bot_id')
      .eq('id', documentId)
      .single();

    if (error || !data) throw new Error('Document not found');
    return data.bot_id;
  }

  /**
   * Get document statistics for a bot
   */
  static async getDocumentStats(botId: string): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    totalSize: number;
  }> {
    try {
      const documents = await this.getDocuments(botId);
      
      const stats = {
        total: documents.length,
        byStatus: {} as Record<string, number>,
        byType: {} as Record<string, number>,
        totalSize: 0,
      };

      documents.forEach(doc => {
        // Count by status
        stats.byStatus[doc.processing_status] = (stats.byStatus[doc.processing_status] || 0) + 1;
        
        // Count by type
        const fileType = doc.file_type || 'unknown';
        stats.byType[fileType] = (stats.byType[fileType] || 0) + 1;
        
        // Sum file sizes
        stats.totalSize += doc.file_size || 0;
      });

      return stats;
    } catch (error) {
      console.error('Error getting document stats:', error);
      throw error;
    }
  }
}
