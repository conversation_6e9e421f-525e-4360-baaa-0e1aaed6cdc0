/**
 * Bot Knowledge Base Component
 *
 * Upload, organize, and manage documents that train your bot.
 * Real implementation with Supabase integration for document management.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  ArrowLeft,
  Bot as BotIcon,
  FileText,
  Upload,
  Search,
  Filter,
  Plus,
  AlertCircle,
  BarChart3,
  Settings,
  Download,
  Trash2,
  Eye,
  Edit,
  RefreshCw,
  CheckCircle,
  Clock,
  XCircle,
  Loader2,
  HardDrive,
  FileType,
  Calendar,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { FileUpload } from '../KnowledgeBase/FileUpload';
import { getBot } from '../../services/botService';
import { KnowledgeService, type FileUploadData, type DocumentSearchOptions } from '../../services/knowledgeService';
import type { Database } from '../../types/supabase';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

type Bot = Database['public']['Tables']['bots']['Row'];
type KnowledgeDocument = Database['public']['Tables']['knowledge_documents']['Row'];

const BotKnowledge: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bot, setBot] = useState<Bot | null>(null);
  const [documents, setDocuments] = useState<KnowledgeDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [stats, setStats] = useState<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    totalSize: number;
  } | null>(null);

  useEffect(() => {
    if (id) {
      loadBotAndDocuments(id);
    }
  }, [id]);

  const loadBotAndDocuments = async (botId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Load bot and documents in parallel
      const [botData, documentsData, statsData] = await Promise.all([
        getBot(botId),
        KnowledgeService.getDocuments(botId, getSearchOptions()),
        KnowledgeService.getDocumentStats(botId)
      ]);

      setBot(botData);
      setDocuments(documentsData);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading bot and documents:', err);
      setError('Failed to load knowledge base information.');
    } finally {
      setLoading(false);
    }
  };

  const getSearchOptions = (): DocumentSearchOptions => {
    const options: DocumentSearchOptions = {
      sortBy: 'created_at',
      sortOrder: 'desc',
    };

    if (searchQuery.trim()) {
      options.query = searchQuery.trim();
    }

    if (statusFilter !== 'all') {
      options.status = [statusFilter];
    }

    return options;
  };

  const handleSearch = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const documentsData = await KnowledgeService.getDocuments(id, getSearchOptions());
      setDocuments(documentsData);
    } catch (err) {
      console.error('Error searching documents:', err);
      setError('Failed to search documents.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (uploadData: FileUploadData) => {
    if (!id) return;

    try {
      setUploading(true);
      await KnowledgeService.uploadDocument(id, uploadData);
      setShowUploadDialog(false);

      // Reload documents and stats
      await loadBotAndDocuments(id);
    } catch (err) {
      console.error('Error uploading document:', err);
      throw err; // Let FileUpload component handle the error display
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (documentId: string) => {
    if (!id || !confirm('Are you sure you want to delete this document? This action cannot be undone.')) {
      return;
    }

    try {
      await KnowledgeService.deleteDocument(documentId);
      await loadBotAndDocuments(id);
    } catch (err) {
      console.error('Error deleting document:', err);
      setError('Failed to delete document.');
    }
  };

  const handleDownload = async (document: KnowledgeDocument) => {
    try {
      const downloadUrl = await KnowledgeService.getDownloadUrl(document.id);
      window.open(downloadUrl, '_blank');
    } catch (err) {
      console.error('Error downloading document:', err);
      setError('Failed to download document.');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      ready: 'default',
      processing: 'secondary',
      error: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status}</span>
      </Badge>
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading && !documents.length) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading knowledge base..." />
        </div>
      </div>
    );
  }

  if (error && !bot) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/bots')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bots
          </Button>
        </div>

        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-center flex items-center justify-center gap-2">
              <AlertCircle className="h-5 w-5" />
              <div>
                <p className="font-medium">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => id && loadBotAndDocuments(id)}
                  className="mt-2"
                >
                  Try Again
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/bots/${bot?.id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bot Details
          </Button>
        </div>

        <div className="flex items-center gap-3">
          <div className="p-3 bg-primary/10 rounded-lg">
            <FileText className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Knowledge Base - {bot?.name}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge
                variant={bot?.status === 'active' ? 'default' : 'secondary'}
                className={
                  bot?.status === 'active'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                }
              >
                {bot?.status}
              </Badge>
              <Badge variant="outline">
                {bot?.type === 'faq' ? 'FAQ Bot' : 'Lead Generation'}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{stats.total}</p>
                  <p className="text-sm text-muted-foreground">Total Documents</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <HardDrive className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{formatFileSize(stats.totalSize)}</p>
                  <p className="text-sm text-muted-foreground">Total Size</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold">{stats.byStatus.ready || 0}</p>
                  <p className="text-sm text-muted-foreground">Ready</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <Loader2 className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold">{stats.byStatus.processing || 0}</p>
                  <p className="text-sm text-muted-foreground">Processing</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-10"
            />
          </div>
          <Button onClick={handleSearch} variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="ready">Ready</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="error">Error</SelectItem>
            </SelectContent>
          </Select>

          <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Upload Documents
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Upload Knowledge Documents</DialogTitle>
                <DialogDescription>
                  Upload documents to train your bot. Supported formats: PDF, DOC, DOCX, TXT, MD, CSV, XLS, XLSX
                </DialogDescription>
              </DialogHeader>
              <FileUpload
                onUpload={handleUpload}
                onCancel={() => setShowUploadDialog(false)}
                maxFileSize={10} // 10MB
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-destructive mb-6">
          <CardContent className="pt-6">
            <div className="text-destructive flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setError(null)}
                className="ml-auto"
              >
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents List */}
      {loading ? (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="lg" text="Loading documents..." />
            </div>
          </CardContent>
        </Card>
      ) : documents.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No documents found</h3>
              <p className="text-muted-foreground mb-6">
                {searchQuery || statusFilter !== 'all'
                  ? 'No documents match your search criteria. Try adjusting your filters.'
                  : 'Upload your first document to start training your bot.'
                }
              </p>
              {!searchQuery && statusFilter === 'all' && (
                <Button onClick={() => setShowUploadDialog(true)} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Upload Your First Document
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {documents.map((document) => (
            <Card key={document.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-medium truncate">{document.title}</h3>
                        {getStatusBadge(document.processing_status)}
                      </div>

                      {document.content && (
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                          {document.content.substring(0, 150)}...
                        </p>
                      )}

                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <FileType className="h-4 w-4" />
                          {document.file_type?.split('/').pop()?.toUpperCase() || 'Unknown'}
                        </span>
                        <span className="flex items-center gap-1">
                          <HardDrive className="h-4 w-4" />
                          {formatFileSize(document.file_size || 0)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDistanceToNow(new Date(document.created_at), { addSuffix: true })}
                        </span>
                      </div>
                    </div>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleDownload(document)}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(document.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-8 flex flex-wrap gap-4">
        <Button
          variant="outline"
          onClick={() => navigate(`/bots/${bot?.id}/analytics`)}
          className="flex items-center gap-2"
        >
          <BarChart3 className="h-4 w-4" />
          View Analytics
        </Button>
        <Button
          variant="outline"
          onClick={() => navigate(`/bots/${bot?.id}/conversations`)}
          className="flex items-center gap-2"
        >
          <BotIcon className="h-4 w-4" />
          View Conversations
        </Button>
        <Button
          variant="outline"
          onClick={() => navigate(`/bots/${bot?.id}/settings`)}
          className="flex items-center gap-2"
        >
          <Settings className="h-4 w-4" />
          Bot Settings
        </Button>
      </div>
    </div>
  );
};

export default BotKnowledge;
