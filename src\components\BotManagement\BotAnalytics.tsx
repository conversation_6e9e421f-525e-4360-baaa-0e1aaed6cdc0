/**
 * Bot Analytics Component
 *
 * Detailed analytics and performance metrics for a specific bot.
 * Uses the existing AnalyticsDashboard with real data from Supabase.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Bot as BotIcon, BarChart3, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { AnalyticsDashboard } from '../Dashboard/AnalyticsDashboard';
import { getBot } from '../../services/botService';
import type { Database } from '../../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];

const BotAnalytics: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [bot, setBot] = useState<Bot | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      loadBot(id);
    }
  }, [id]);

  const loadBot = async (botId: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await getBot(botId);
      setBot(data);
    } catch (err) {
      console.error('Error loading bot:', err);
      setError('Failed to load bot information.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading bot analytics..." />
        </div>
      </div>
    );
  }

  if (error || !bot || !id) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/bots')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bots
          </Button>
        </div>

        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-center flex items-center justify-center gap-2">
              <AlertCircle className="h-5 w-5" />
              <div>
                <p className="font-medium">{error || 'Bot not found'}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => id && loadBot(id)}
                  className="mt-2"
                >
                  Try Again
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/bots/${bot.id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Bot Details
          </Button>
        </div>

        <div className="flex items-center gap-3">
          <div className="p-3 bg-primary/10 rounded-lg">
            <BarChart3 className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Analytics - {bot.name}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge
                variant={bot.status === 'active' ? 'default' : 'secondary'}
                className={
                  bot.status === 'active'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
                }
              >
                {bot.status}
              </Badge>
              <Badge variant="outline">
                {bot.type === 'faq' ? 'FAQ Bot' : 'Lead Generation'}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Dashboard */}
      <AnalyticsDashboard botId={id} />

      {/* Quick Actions */}
      <div className="flex gap-4 mt-8">
        <Button asChild variant="outline">
          <Link to={`/bots/${bot.id}`}>
            <BotIcon className="h-4 w-4 mr-2" />
            Bot Details
          </Link>
        </Button>

        <Button asChild variant="outline">
          <Link to={`/bots/${bot.id}/conversations`}>
            View Conversations
          </Link>
        </Button>

        <Button asChild>
          <Link to={`/bots/${bot.id}/edit`}>
            Edit Bot
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default BotAnalytics;
