/**
 * Topics Chart Component
 * 
 * Displays popular topics and questions using horizontal bar charts.
 * Shows what users are asking about most frequently.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart, 
  Bar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { MessageCircle, TrendingUp, Hash, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

import { AnalyticsService } from '@/services/analyticsService';

interface TopicsChartProps {
  botId: string;
  dateRange: { from: Date; to: Date };
  className?: string;
}

interface TopicData {
  topic: string;
  count: number;
  percentage: number;
}

/**
 * Custom tooltip for topics chart
 */
function TopicsTooltip({ active, payload, label }: any) {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3 max-w-xs">
        <p className="font-medium text-sm mb-2">
          {data.topic}
        </p>
        <div className="space-y-1 text-sm">
          <div className="flex items-center justify-between gap-4">
            <span className="text-muted-foreground">Questions:</span>
            <span className="font-medium">{data.count.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-muted-foreground">Percentage:</span>
            <span className="font-medium">{data.percentage}%</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
}

export function TopicsChart({ 
  botId, 
  dateRange,
  className 
}: TopicsChartProps) {
  const [topicsData, setTopicsData] = useState<TopicData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [limit, setLimit] = useState(10);

  // Color scheme for topics (gradient from most to least popular)
  const getTopicColor = (index: number, total: number) => {
    const intensity = 1 - (index / total);
    const hue = 220; // Blue hue
    const saturation = 70 + (intensity * 30); // 70-100%
    const lightness = 45 + (intensity * 15); // 45-60%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  /**
   * Load topics data
   */
  const loadTopicsData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await AnalyticsService.getTopicBreakdown(
        botId,
        dateRange.from.toISOString(),
        dateRange.to.toISOString()
      );
      setTopicsData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load topics data');
      console.error('Failed to load topics data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTopicsData();
  }, [botId, dateRange, limit]);

  // Calculate summary statistics
  const totalQuestions = topicsData.reduce((sum, d) => sum + d.count, 0);
  const topTopic = topicsData[0];
  const averageQuestionsPerTopic = topicsData.length > 0 ? totalQuestions / topicsData.length : 0;

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Popular Topics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="animate-pulse text-muted-foreground">Loading topics data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Popular Topics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex flex-col items-center justify-center gap-4">
            <p className="text-red-600 text-center">{error}</p>
            <Button onClick={loadTopicsData} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Popular Topics
            </CardTitle>
            <CardDescription>
              Most frequently asked questions and topics
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setLimit(limit === 10 ? 20 : 10)}
            >
              Show {limit === 10 ? 'More' : 'Less'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadTopicsData}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Summary stats */}
        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {totalQuestions.toLocaleString()} total questions
            </Badge>
            <Badge variant="outline" className="text-xs">
              {topicsData.length} topics tracked
            </Badge>
          </div>
          {topTopic && (
            <div className="flex items-center gap-2">
              <Badge variant="default" className="text-xs">
                Top: {topTopic.topic} ({topTopic.percentage}%)
              </Badge>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={topicsData}
              layout="horizontal"
              margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                type="number"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => value.toLocaleString()}
              />
              <YAxis 
                type="category"
                dataKey="topic"
                tick={{ fontSize: 11 }}
                width={90}
                tickFormatter={(value) => {
                  // Truncate long topic names
                  return value.length > 15 ? `${value.substring(0, 12)}...` : value;
                }}
              />
              <Tooltip content={<TopicsTooltip />} />
              <Bar 
                dataKey="count" 
                radius={[0, 4, 4, 0]}
              >
                {topicsData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={getTopicColor(index, topicsData.length)} 
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
        
        {/* Detailed topic list */}
        <div className="mt-6 pt-4 border-t">
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Hash className="h-4 w-4" />
            Topic Breakdown
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {topicsData.slice(0, 8).map((topic, index) => (
              <div 
                key={topic.topic}
                className="flex items-center justify-between p-3 rounded-lg bg-muted/50"
              >
                <div className="flex items-center gap-3">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: getTopicColor(index, topicsData.length) }}
                  />
                  <div>
                    <div className="text-sm font-medium">{topic.topic}</div>
                    <div className="text-xs text-muted-foreground">
                      Rank #{index + 1}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{topic.count.toLocaleString()}</div>
                  <div className="text-xs text-muted-foreground">{topic.percentage}%</div>
                </div>
              </div>
            ))}
          </div>
          
          {topicsData.length > 8 && (
            <div className="mt-3 text-center">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setLimit(limit + 10)}
              >
                Show {Math.min(10, topicsData.length - 8)} more topics
              </Button>
            </div>
          )}
        </div>
        
        {/* Summary insights */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold">
              {topTopic?.count.toLocaleString() || '0'}
            </div>
            <div className="text-xs text-muted-foreground">Most Popular Topic</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {Math.round(averageQuestionsPerTopic).toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">Avg per Topic</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {topicsData.filter(t => t.percentage >= 5).length}
            </div>
            <div className="text-xs text-muted-foreground">Major Topics (≥5%)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {topicsData.slice(0, 3).reduce((sum, t) => sum + t.percentage, 0).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Top 3 Coverage</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
