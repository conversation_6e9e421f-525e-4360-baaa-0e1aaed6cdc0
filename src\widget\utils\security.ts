/**
 * Widget Security Utilities
 * 
 * Security utilities for the embeddable widget to prevent XSS and other attacks.
 */

import type { WidgetConfig } from '../types';

/**
 * Create a secure container for the widget
 */
export function createSecureContainer(position: 'bottom-right' | 'bottom-left' = 'bottom-right'): HTMLElement {
  const container = document.createElement('div');
  
  // Set secure attributes
  container.id = `webton-chatbot-${Date.now()}`;
  container.className = 'webton-chatbot-container';
  
  // Apply secure styles
  const styles: Record<string, string> = {
    position: 'fixed',
    zIndex: 'var(--webton-z-base, 2147483640)',
    bottom: '20px',
    [position === 'bottom-right' ? 'right' : 'left']: '20px',
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#333333',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '0',
    boxShadow: 'none',
    maxWidth: '400px',
    maxHeight: '600px',
    margin: '0',
    padding: '0',
    overflow: 'visible',
    pointerEvents: 'auto',
    userSelect: 'none',
    webkitUserSelect: 'none',
    mozUserSelect: 'none',
    msUserSelect: 'none',
    webkitTouchCallout: 'none',
    webkitTapHighlightColor: 'transparent',
  };

  // Apply styles securely
  Object.entries(styles).forEach(([property, value]) => {
    container.style.setProperty(property, value, 'important');
  });

  // Add security attributes
  container.setAttribute('data-webton-widget', 'true');
  container.setAttribute('role', 'complementary');
  container.setAttribute('aria-label', 'Chat widget');

  return container;
}

/**
 * Sanitize HTML content to prevent XSS
 */
export function sanitizeHtml(html: string): string {
  if (typeof html !== 'string') {
    return '';
  }

  // Create a temporary element to parse HTML
  const temp = document.createElement('div');
  temp.textContent = html; // This automatically escapes HTML
  
  return temp.innerHTML;
}

/**
 * Sanitize user input for messages
 */
export function sanitizeMessage(message: string): string {
  if (typeof message !== 'string') {
    return '';
  }

  // Remove HTML tags and scripts
  let sanitized = message.replace(/<[^>]*>/g, '');
  
  // Remove javascript: and data: URLs
  sanitized = sanitized.replace(/javascript:/gi, '');
  sanitized = sanitized.replace(/data:/gi, '');
  
  // Remove potentially dangerous characters
  sanitized = sanitized.replace(/[<>'"&]/g, (char) => {
    const entities: Record<string, string> = {
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '&': '&amp;',
    };
    return entities[char] || char;
  });

  // Trim and limit length
  return sanitized.trim().substring(0, 1000);
}

/**
 * Validate and sanitize URLs
 */
export function sanitizeUrl(url: string): string {
  if (typeof url !== 'string') {
    return '';
  }

  try {
    const urlObj = new URL(url);
    
    // Only allow HTTP and HTTPS protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return '';
    }

    return urlObj.toString();
  } catch {
    return '';
  }
}

/**
 * Create Content Security Policy for the widget
 */
export function createCSP(): string {
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' https://api.webton-chatbots.com wss://api.webton-chatbots.com",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ];

  return csp.join('; ');
}

/**
 * Apply security headers to requests
 */
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': createCSP(),
  };
}

/**
 * Rate limiting for API requests
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;

  constructor(windowMs: number = 60000, maxRequests: number = 100) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  isAllowed(key: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;

    // Get existing requests for this key
    let requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // Check if we're under the limit
    if (requests.length >= this.maxRequests) {
      return false;
    }

    // Add current request
    requests.push(now);
    this.requests.set(key, requests);

    return true;
  }

  reset(key: string): void {
    this.requests.delete(key);
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();

/**
 * Validate message content for security
 */
export function validateMessage(message: string): boolean {
  if (typeof message !== 'string') {
    return false;
  }

  // Check length
  if (message.length === 0 || message.length > 1000) {
    return false;
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /data:/i,
    /vbscript:/i,
    /on\w+\s*=/i, // Event handlers like onclick=
    /expression\s*\(/i, // CSS expressions
  ];

  return !suspiciousPatterns.some(pattern => pattern.test(message));
}

/**
 * Generate a secure session ID
 */
export function generateSecureSessionId(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Validate origin against allowed origins
 */
export function validateOrigin(allowedOrigins?: string[]): boolean {
  if (!allowedOrigins || allowedOrigins.length === 0) {
    return true;
  }

  const currentOrigin = window.location.origin;
  
  return allowedOrigins.some(origin => {
    if (origin === '*') {
      return true;
    }
    
    if (origin === currentOrigin) {
      return true;
    }
    
    // Support wildcard subdomains
    if (origin.startsWith('*.')) {
      const domain = origin.substring(2);
      return currentOrigin.endsWith(`.${domain}`) || currentOrigin === `https://${domain}`;
    }
    
    return false;
  });
}

/**
 * Prevent clickjacking attacks
 */
export function preventClickjacking(): void {
  // Check if we're in an iframe
  if (window.self !== window.top) {
    // We're in an iframe, check if it's allowed
    try {
      // Try to access parent origin
      const parentOrigin = window.parent.location.origin;
      console.log('[WebtonChatbot] Running in iframe from:', parentOrigin);
    } catch {
      // Cross-origin iframe, this is expected for embedded widgets
      console.log('[WebtonChatbot] Running in cross-origin iframe');
    }
  }
}

/**
 * Secure event listener attachment
 */
export function addSecureEventListener(
  element: Element,
  event: string,
  handler: EventListener,
  options?: AddEventListenerOptions
): void {
  // Validate inputs
  if (!element || typeof event !== 'string' || typeof handler !== 'function') {
    console.error('[WebtonChatbot] Invalid event listener parameters');
    return;
  }

  // Add event listener with security options
  const secureOptions = {
    passive: true,
    capture: false,
    ...options,
  };

  element.addEventListener(event, handler, secureOptions);
}

/**
 * Secure message posting for cross-origin communication
 */
export function postSecureMessage(
  targetWindow: Window,
  message: any,
  targetOrigin: string
): void {
  try {
    // Validate target origin
    if (!targetOrigin || targetOrigin === '*') {
      console.error('[WebtonChatbot] Target origin must be specified');
      return;
    }

    // Sanitize message
    const sanitizedMessage = {
      type: 'webton-widget-message',
      timestamp: Date.now(),
      data: message,
    };

    targetWindow.postMessage(sanitizedMessage, targetOrigin);
  } catch (error) {
    console.error('[WebtonChatbot] Failed to post secure message:', error);
  }
}

/**
 * Initialize security measures
 */
export function initializeSecurity(config: WidgetConfig): void {
  // Prevent clickjacking
  preventClickjacking();

  // Validate origin
  if (!validateOrigin(config.allowedOrigins)) {
    throw new Error('Origin not allowed');
  }

  // Set up CSP if supported
  if (typeof document !== 'undefined' && document.head) {
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = createCSP();
    document.head.appendChild(meta);
  }

  console.log('[WebtonChatbot] Security measures initialized');
}
