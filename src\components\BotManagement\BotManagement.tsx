/**
 * Bot Management Component
 * 
 * Main bot listing page with grid layout, search, filters, and create button.
 */

import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { 
  Plus, 
  Search, 
  Filter, 
  Bot as BotIcon,
  ArrowLeft 
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import BotCard from './BotCard';
import { getBots } from '../../services/botService';
import type { Database } from '../../types/supabase';

type Bot = Database['public']['Tables']['bots']['Row'];

const BotManagement: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');

  // Load bots on component mount
  useEffect(() => {
    loadBots();
  }, []);

  const loadBots = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getBots();
      setBots(data);
    } catch (err) {
      console.error('Error loading bots:', err);
      setError('Failed to load bots. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter bots based on search term and status
  const filteredBots = bots.filter(bot => {
    const matchesSearch = bot.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || bot.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const handleBotDeleted = (deletedBotId: string) => {
    setBots(prevBots => prevBots.filter(bot => bot.id !== deletedBotId));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-6 md:p-10">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading bots..." />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
              <BotIcon className="h-8 w-8" />
              Bot Management
            </h1>
            <p className="mt-1 text-muted-foreground">
              Create and manage your AI chatbots
            </p>
          </div>
          
          <Button asChild>
            <Link to="/bots/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New Bot
            </Link>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search bots..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant={filterStatus === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('all')}
              >
                All
              </Button>
              <Button
                variant={filterStatus === 'active' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('active')}
              >
                Active
              </Button>
              <Button
                variant={filterStatus === 'inactive' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('inactive')}
              >
                Inactive
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-center">
              <p>{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={loadBots}
                className="mt-2"
              >
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bot Grid */}
      {filteredBots.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <BotIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {searchTerm || filterStatus !== 'all' ? 'No bots found' : 'No bots yet'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || filterStatus !== 'all' 
                  ? 'Try adjusting your search or filters'
                  : 'Create your first bot to get started'
                }
              </p>
              {!searchTerm && filterStatus === 'all' && (
                <Button asChild>
                  <Link to="/bots/new" className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Create Your First Bot
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBots.map((bot) => (
            <BotCard 
              key={bot.id} 
              bot={bot} 
              onDeleted={handleBotDeleted}
            />
          ))}
        </div>
      )}

      {/* Stats */}
      {bots.length > 0 && (
        <div className="mt-8 text-center text-sm text-muted-foreground">
          Showing {filteredBots.length} of {bots.length} bots
        </div>
      )}
    </div>
  );
};

export default BotManagement;
