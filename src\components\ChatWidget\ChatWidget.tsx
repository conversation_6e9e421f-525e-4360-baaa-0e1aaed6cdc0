import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import ChatBubble from "./ChatBubble";
import ChatInterface from "./ChatInterface";

export interface ChatWidgetProps {
  companyName?: string;
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  greetingMessage?: string;
  position?: "bottom-right" | "bottom-left";
  isOpen?: boolean;
  onToggle?: () => void;
}

const ChatWidget: React.FC<ChatWidgetProps> = ({
  companyName = "Company Name",
  logo = "https://api.dicebear.com/7.x/avataaars/svg?seed=company",
  primaryColor = "#4f46e5",
  secondaryColor = "#ffffff",
  greetingMessage = "Hello! How can I help you today?",
  position = "bottom-right",
  isOpen: initialIsOpen = false,
  onToggle,
}) => {
  const [isOpen, setIsOpen] = useState(initialIsOpen);

  const handleToggle = () => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);
    if (onToggle) {
      onToggle();
    }
  };

  const positionClasses = {
    "bottom-right": "right-0 bottom-0",
    "bottom-left": "left-0 bottom-0",
  };

  return (
    <div
      className={`${positionClasses[position]} z-50`}
      style={{ fontFamily: '"Inter", sans-serif' }}
    >
      <AnimatePresence mode="wait">
        {isOpen ? (
          <motion.div
            key="chat-interface"
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
            className="shadow-lg rounded-lg overflow-hidden"
          >
            <div className="w-80 h-96 sm:w-80 sm:h-96 md:w-96 md:h-[500px] max-w-[calc(100vw-2rem)] max-h-[calc(100vh-2rem)] min-w-[280px] min-h-[400px]">
              <ChatInterface
                companyName={companyName}
                botAvatar={logo}
                accentColor={primaryColor}
                onClose={handleToggle}
                initialMessages={[
                  {
                    id: "1",
                    content: greetingMessage,
                    sender: "bot",
                    timestamp: new Date(),
                  },
                ]}
              />
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="chat-bubble"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            <ChatBubble
              logo={logo}
              primaryColor={primaryColor}
              secondaryColor={secondaryColor}
              onClick={handleToggle}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChatWidget;
